acres==0.3.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
altair==5.5.0
annotated-types==0.7.0
anyio==4.9.0
appnope==0.1.4
asttokens==3.0.0
async-timeout==4.0.3
attrs==25.1.0
beautifulsoup4==4.13.3
bitarray==3.1.0
blinker==1.9.0
Bottleneck==1.4.2
Brotli==1.0.9
bs4==0.0.2
CacheControl==0.14.2
cachetools==5.5.1
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
ci-info==0.3.0
click==8.1.8
cmdstanpy==1.2.5
comm==0.2.2
configobj==5.0.9
configparser==7.2.0
contourpy==1.3.1
cryptography==44.0.2
cycler==0.12.1
dataclasses-json==0.6.7
dateparser==1.2.1
debugpy==1.8.12
langchain_nvidia_ai_endpoints
decorator==5.1.1
deprecation==2.1.0
distro==1.9.0
dotenv==0.9.9
entrypoints==0.4
etelemetry==0.3.1
exceptiongroup==1.2.2
faiss-cpu
executing==2.2.0
faiss-cpu==1.10.0
Faker==37.1.0
favicon==0.7.0
filelock==3.18.0
firebase-admin==6.7.0
fitz==0.0.1.dev2
fonttools==4.56.0
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2025.3.0
gitdb==4.0.12
GitPython==3.1.44
google==3.0.0
google-ai-generativelanguage==0.6.15
google-api-core==2.24.1
google-api-python-client==2.161.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.3
google-cloud-firestore==2.20.1
google-cloud-storage==3.1.0
google-crc32c==1.7.0
google-genai==1.2.0
google-generativeai==0.8.4
google-resumable-media==2.7.2
googleapis-common-protos==1.68.0
GoogleNews==1.6.15
groq==0.20.0
grpcio==1.70.0
grpcio-status==1.70.0
h11==0.14.0
h2==4.2.0
holidays==0.69
hpack==4.1.0
htbuilder==0.9.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.3
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
ipykernel==6.29.5
ipython==8.32.0
isodate==0.6.1
jedi==0.19.2
jinaai==0.2.10
Jinja2==3.1.5
jiter==0.9.0
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
kiwisolver==1.4.8
langchain==0.3.21
langchain-community==0.3.20
langchain-core==0.3.47
langchain-groq==0.3.1
langchain-nvidia-ai-endpoints==0.3.9
langchain-text-splitters==0.3.7
langgraph==0.3.18
langgraph-checkpoint==2.0.21
langgraph-prebuilt==0.1.4
langgraph-sdk==0.1.58
langsmith==0.3.18
looseversion==1.3.0
lxml==5.3.1
Markdown==3.7
markdown-it-py==3.0.0
markdownlit==0.0.7
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.1.0
multidict==6.2.0
multitasking==0.0.11
mypy-extensions==1.0.0
narwhals==1.27.1
nest-asyncio==1.6.0
networkx==3.4.2
newsapi-python==0.2.7
nibabel==5.3.2
nipype==1.10.0
numexpr==2.10.1
numpy==2.0.1
openai==1.68.2
orjson==3.10.15
openai
packaging==24.2
pandas==2.2.3
parso==0.8.4
pathlib==1.0.1
pdfreader==0.1.15
peewee==3.17.9
pexpect==4.9.0
pillow==11.1.0
pip==25.0
pkgutil_resolve_name==1.3.10
platformdirs==4.3.6
plotly==6.0.1
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.3.0
prophet==1.1.6
proto-plus==1.26.0
protobuf==5.29.3
prov==2.0.1
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
puremagic==1.28
pyarrow==19.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycountry==24.6.1
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.10.6
pydantic_core==2.27.2
pydantic-settings==2.8.1
pydeck==0.9.1
pydot==3.0.4
Pygments==2.19.1
PyJWT==2.10.1
pymdown-extensions==10.14.3
PyMuPDF==1.25.4
pyparsing==3.2.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2025.1
pyxnat==1.6.3
PyYAML==6.0.2
pyzmq==26.2.1
rdflib==6.3.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.22.3
rsa==4.9
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==3.4.1
setuptools==75.8.0
simplejson==3.20.1
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.39
st-annotated-text==4.0.2
st-theme==1.2.3
stack-data==0.6.3
stanio==0.5.1
streamlit==1.42.1
streamlit-antd-components==0.3.2
streamlit-avatar==0.1.3
streamlit-camera-input-live==0.2.0
streamlit-card==1.0.2
streamlit-embedcode==0.1.2
streamlit-extras==0.6.0
streamlit-faker==0.0.3
streamlit-image-coordinates==0.1.9
streamlit-keyup==0.3.0
streamlit-lottie==0.0.5
streamlit-modal==0.1.2
streamlit-navigation-bar==3.3.0
streamlit-toggle-switch==1.0.2
streamlit-vertical-slider==2.5.5
sympy==1.13.1
tavily-python==0.5.1
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
torch==2.6.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
traits==7.0.2
transformers==4.50.0
typing_extensions==4.12.2
typing-inspect==0.9.0
tzdata==2025.1
tzlocal==5.3.1
uritemplate==4.1.1
urllib3==2.3.0
validators==0.34.0
watchdog==4.0.2
wcwidth==0.2.13
websockets==14.2
wheel==0.45.1
yarl==1.18.3
yfinance==0.2.55
zipp==3.21.0
zstandard==0.23.0
