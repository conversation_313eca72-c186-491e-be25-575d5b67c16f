# RAG Integration Guide

## Overview

The RAG (Retrieval-Augmented Generation) functionality has been successfully integrated into the Financial ChatBot. This feature allows users to query a knowledge base using AWS Bedrock and retrieve relevant information from stored documents.

## Features Added

### 1. New Route: `RAG_query`
The chatbot now includes a new routing option for RAG queries. The system automatically detects when a user wants to search the knowledge base.

### 2. RAG Processing Function
- `process_rag_query()`: Handles RAG queries using the BedrockRAGHandler
- Returns formatted responses with sources
- Handles errors gracefully

### 3. Updated Routing Logic
The routing system now recognizes RAG-related keywords and phrases:
- "rag"
- "knowledge base"
- "documents"
- "search documents"
- "find in documents"
- "what does the knowledge base say about"

## How to Use

### Example Queries
Users can now ask questions like:
- "Search the knowledge base for risk management strategies"
- "What does the documentation say about portfolio optimization?"
- "Find documents about ESG investing"
- "RAG search for market analysis"
- "Knowledge base query about derivatives"

### Response Format
RAG responses include:
1. **Answer**: The generated response from the knowledge base
2. **Sources**: List of source documents used to generate the answer

## Configuration

### Environment Variables
The RAG functionality requires the following environment variables:
- `AWS_REGION` (default: "us-east-1")
- `BEDROCK_KNOWLEDGE_BASE_ID` (default: "NZW3OFZAJP")
- `BEDROCK_MODEL_ARN` (default: Claude 3 Sonnet model)

### AWS Credentials
Ensure your AWS credentials are properly configured for Bedrock access.

## Technical Implementation

### Files Modified
1. **ChatBot/chatbot.py**:
   - Added `RAG_query` to routing options
   - Imported `BedrockRAGHandler`
   - Added `process_rag_query()` function
   - Updated state graph with RAG node
   - Added routing examples for RAG queries

2. **ChatBot/bedrock_rag.py**:
   - Existing file with `BedrockRAGHandler` class
   - Handles AWS Bedrock knowledge base queries

### State Graph Updates
- Added `process_rag_query` node
- Connected RAG processing to evaluation pipeline
- Integrated with existing routing system

## Error Handling

The RAG integration includes robust error handling:
- AWS credential issues
- Bedrock service errors
- Knowledge base connectivity problems
- Malformed responses

Errors are displayed to users in a user-friendly format.

## Testing

The integration has been tested for:
- ✅ Proper routing of RAG queries
- ✅ Correct integration with existing chatbot flow
- ✅ Error handling without AWS credentials
- ✅ Expected response structure

## Usage in Streamlit App

Users can now interact with the RAG functionality through the existing chatbot interface. Simply type queries that mention knowledge base searches, and the system will automatically route them to the RAG processing pipeline.

## Future Enhancements

Potential improvements:
1. Support for multiple knowledge bases
2. Advanced filtering options
3. Document upload functionality
4. Enhanced source citation formatting
5. Caching for frequently asked questions

## Troubleshooting

### Common Issues
1. **AWS Credentials**: Ensure proper AWS configuration
2. **Knowledge Base Access**: Verify Bedrock permissions
3. **Model Availability**: Check if the specified model is available in your region

### Error Messages
- "RAG Error: Bedrock client not initialized" - Check AWS credentials
- "Error querying knowledge base" - Verify knowledge base ID and permissions
