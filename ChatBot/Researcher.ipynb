{"cells": [{"cell_type": "code", "execution_count": null, "id": "31a6df2f", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from urllib.parse import urljoin, urlparse\n", "from tavily import TavilyClient\n", "import google.generativeai as genai\n", "\n", "# --- Configuration & Blocklists ---\n", "# DOMAIN_BLOCKLIST is commented out to allow scraping from news sites, etc.\n", "# DOMAIN_BLOCKLIST = {\n", "#     'wikipedia.org', 'linkedin.com', 'youtube.com', 'facebook.com', 'twitter.com',\n", "#     'instagram.com', 'bloomberg.com', 'reuters.com', 'forbes.com', 'cnbc.com',\n", "#     'marketwatch.com', 'finance.yahoo.com', 'crunchbase.com'\n", "# }\n", "DEFAULT_KEYWORDS = ['investor relations', 'investors', 'financials', 'sec filings', 'berichte', 'finanzberichte']\n", "\n", "# --- LLM Functions ---\n", "def get_dynamic_keywords_from_llm(company_name, context_text):\n", "    \"\"\"\n", "    Uses Gemini to generate context-aware keywords.\n", "    While less critical for finding a specific page, it can still help the LLM\n", "    understand the company's financial context when aggregating information.\n", "    \"\"\"\n", "    print(\"\\n-> Asking Gemini LLM to generate dynamic keywords from website content for context...\")\n", "    try:\n", "        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "\n", "        prompt = f\"\"\"\n", "        Based on the following text scraped from various sources related to '{company_name}', act as a financial web analyst.\n", "        Your task is to generate a concise, comma-separated list of keywords that represent key financial terms or areas of interest for this company.\n", "        Consider the company's industry, language, and potential corporate structure from the context.\n", "\n", "        For example: 'quarterly earnings, annual report, revenue, net income, stock performance, investor call'\n", "\n", "        Scraped Text Context:\n", "        ---\n", "        {context_text[:8000]}\n", "        ---\n", "\n", "        Provide only the comma-separated list of keywords.\n", "        \"\"\"\n", "        response = model.generate_content(prompt, generation_config={\"temperature\": 0.2})\n", "        keywords = [kw.strip() for kw in response.candidates[0].content.parts[0].text.strip().lower().split(',')]\n", "        print(f\"   Gemini LLM generated keywords: {keywords}\")\n", "        return keywords\n", "    except Exception as e:\n", "        print(f\"   [Warning] Gemini LLM call failed: {e}. Falling back to default keywords.\")\n", "        return DEFAULT_KEYWORDS\n", "\n", "def generate_financial_report(company_name, text_content):\n", "    \"\"\"\n", "    Uses Gemini LLM to analyze aggregated text from various sources\n", "    and generate a financial report.\n", "    \"\"\"\n", "    print(\"\\n-> Generating financial report with Gemini LLM from aggregated content...\")\n", "    max_chars = 25000 # Increased max_chars to accommodate more aggregated content\n", "    if len(text_content) > max_chars:\n", "        print(f\"   (Trimming content from {len(text_content)} to {max_chars} characters for LLM analysis)\")\n", "        text_content = text_content[:max_chars]\n", "\n", "    try:\n", "        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "\n", "        prompt = f\"\"\"\n", "        You are a professional financial analyst. Analyze the following aggregated text scraped from various reliable sources (news articles, company announcements, investor pages, etc.) related to {company_name}.\n", "        Generate a summary of the company's performance for the MOST RECENT financial year you can find within the provided text.\n", "\n", "        Structure your report exactly as follows:\n", "        1.  **Most Recent Financial Year:** Identify and state the year (e.g., \"Fiscal Year 2025\").\n", "        2.  **Key Financial Highlights:**\n", "            *   **Revenue:** State the total revenue.\n", "            *   **Net Income:** State the net income (or profit).\n", "            *   **Earnings Per Share (EPS):** If available, state the EPS.\n", "            *   **Other Significant Mentions:** Briefly include any other crucial financial metrics or achievements (e.g., new product launches affecting revenue, major partnerships, significant R&D investments) found in the text.\n", "        3.  **Management Summary & Outlook:** Briefly summarize any commentary from management about the results, challenges, or future outlook, including any strategic directions or upcoming events.\n", "\n", "        IMPORTANT: If you cannot find a specific figure, clearly state \"Information not found in the provided text.\" Do not invent data. Prioritize information from official announcements or reputable financial news.\n", "\n", "        Aggregated Scraped Text:\n", "        ---\n", "        {text_content}\n", "        ---\n", "        \"\"\"\n", "        response = model.generate_content([{\"role\": \"user\", \"parts\": [prompt]}], generation_config={\"temperature\": 0.1})\n", "        return response.candidates[0].content.parts[0].text\n", "    except Exception as e:\n", "        return f\"[<PERSON><PERSON><PERSON>] Could not generate report from Gemini LLM: {e}\"\n", "\n", "# --- Scraping Functions ---\n", "def simple_scrape(url):\n", "    \"\"\"Scrapes the text content of a single URL.\"\"\"\n", "    try:\n", "        session = requests.Session()\n", "        session.headers.update({'User-Agent': 'Mozilla/5.0'})\n", "        response = session.get(url, timeout=10)\n", "        response.raise_for_status()\n", "        soup = BeautifulSoup(response.text, 'html.parser')\n", "        # Remove script and style elements to clean text\n", "        for script_or_style in soup(['script', 'style']):\n", "            script_or_style.extract()\n", "        text = ' '.join(soup.get_text().split())\n", "        print(f\"   Scraped {len(text)} characters from {url}\")\n", "        return text\n", "    except requests.RequestException as e:\n", "        print(f\"   [<PERSON><PERSON><PERSON>] Could not scrape {url}: {e}\")\n", "        return \"\"\n", "\n", "# The following functions are removed as they are not applicable to the new, broader scraping strategy:\n", "# find_financials_page\n", "# recursive_scrape\n", "\n", "# --- Main Workflow Function ---\n", "def main_workflow(company_name):\n", "    \"\"\"\n", "    Full workflow: search broadly, scrape multiple relevant sources,\n", "    aggregate content, and generate a financial report.\n", "    \"\"\"\n", "    print(f\"Starting broad search for financial information on '{company_name}'...\")\n", "    try:\n", "        tavily_client = TavilyClient(api_key=os.getenv('TAVILY_API_KEY'))\n", "        # Broaden the search query to include news and announcements\n", "        search_query = f\"{company_name} financial news OR investor announcement OR earnings report OR annual results\"\n", "        response = tavily_client.search(search_query, search_depth=\"advanced\", max_results=10) # Get more results\n", "        if not response or 'results' not in response:\n", "            print(\"Tavily search failed or returned no results.\")\n", "            return\n", "        search_results = response['results']\n", "    except Exception as e:\n", "        print(f\"An error occurred during Tavily search: {e}\")\n", "        return\n", "\n", "    # No domain filtering in this version, as we want diverse sources\n", "    potential_sources = [res['url'] for res in search_results]\n", "\n", "    if not potential_sources:\n", "        print(\"No potential sources found after initial search.\")\n", "        return\n", "\n", "    print(f\"\\n-> Scraping content from {len(potential_sources)} potential sources...\")\n", "    full_scraped_corpus = \"\"\n", "    initial_context_urls = potential_sources[:3] # Use a few for initial context for keywords\n", "    for i, url in enumerate(potential_sources):\n", "        print(f\"   Processing source {i+1}/{len(potential_sources)}: {url}\")\n", "        scraped_text = simple_scrape(url)\n", "        if scraped_text:\n", "            full_scraped_corpus += scraped_text + \" \"\n", "        # Collect initial context for dynamic keywords from the first few sources\n", "        if url in initial_context_urls:\n", "            pass # Already covered by full_scraped_corpus accumulation\n", "\n", "    if not full_scraped_corpus:\n", "        print(\"\\n❌ Could not gather any text content from the search results.\")\n", "        return\n", "\n", "    # Generate dynamic keywords based on the initial scraped content for better context\n", "    # This step is now more about understanding the overall context for the LLM report generation\n", "    dynamic_keywords = get_dynamic_keywords_from_llm(company_name, full_scraped_corpus)\n", "    print(f\"   Using dynamic keywords: {dynamic_keywords} (Note: these are for context, not for filtering pages anymore)\")\n", "\n", "\n", "    if full_scraped_corpus and len(full_scraped_corpus) > 500:\n", "        report = generate_financial_report(company_name, full_scraped_corpus)\n", "        print(\"\\n\" + \"=\"*25 + \" FINANCIAL REPORT \" + \"=\"*25)\n", "        print(report)\n", "        print(\"=\"*70)\n", "    else:\n", "        print(\"\\n❌ Could not gather enough text content from the websites to generate a report.\")\n", "\n", "# --- Example Usage ---\n", "if __name__ == \"__main__\":\n", "    # Ensure you have your API keys set as environment variables:\n", "    # os.environ['TAVILY_API_KEY'] = 'YOUR_TAVILY_API_KEY'\n", "    # os.environ['GEMINI_API_KEY'] = 'YOUR_GEMINI_API_KEY'\n", "\n", "    main_workflow(\"NVIDIA\")\n", "    print(\"\\n\" + \"#\"*70 + \"\\n\")\n", "    main_workflow(\"Siemens\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}