{"cells": [{"cell_type": "code", "execution_count": 6, "id": "3993c7c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Industry Company Finder ---\n", "\n", " researching prominent companies in the 5G Connectivity industry...\n", " searching the web for industry data...\n", " extracting company names from search results...\n", "\n", "Found the following prominent companies in the 5G Connectivity industry:\n", "- Samsung\n", "- <PERSON><PERSON><PERSON>\n", "- <PERSON><PERSON>\n"]}], "source": ["import os\n", "from typing import List\n", "\n", "# To load environment variables from a .env file\n", "from dotenv import load_dotenv\n", "\n", "# LangChain components for interacting with the language model\n", "from langchain_groq import ChatGroq\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain.schema.output_parser import StrOutputParser\n", "\n", "# Tavily for performing the web search\n", "from tavily import TavilyClient\n", "\n", "def get_companies_in_industry(industry: str) -> List[str]:\n", "    \"\"\"\n", "    Finds and returns a list of 3 prominent companies within a given industry.\n", "\n", "    This function performs two main steps:\n", "    1.  Uses the Tavily search API to find articles and text about the\n", "        specified industry's major players.\n", "    2.  Feeds this text to a language model (LLM) with a specific prompt\n", "        to extract only the names of 3 companies.\n", "\n", "    Args:\n", "        industry: The industry or domain to research.\n", "\n", "    Returns:\n", "        A list of strings, where each string is the name of a company.\n", "        Returns an empty list if no companies are found.\n", "    \"\"\"\n", "    print(f\"\\n researching prominent companies in the {industry} industry...\")\n", "\n", "    # --- Step 1: Perform a targeted web search ---\n", "    print(\" searching the web for industry data...\")\n", "    search_query = f\"top 3 companies in the {industry} industry\"\n", "    \n", "    # Initialize the Tavily client and perform the search\n", "    api_key = os.environ.get('TAVILY_API_KEY')\n", "    if not api_key:\n", "        raise ValueError(\"TAVILY_API_KEY environment variable not set.\")\n", "    tavily_client = TavilyClient(api_key=api_key)\n", "    \n", "    try:\n", "        # We only need 2-3 good sources to find the top companies\n", "        search_results = tavily_client.search(query=search_query, max_results=3)['results']\n", "        # Combine the content of the search results into a single string\n", "        search_context = \"\\n\\n\".join([result['content'] for result in search_results])\n", "    except Exception as e:\n", "        print(f\"Error during web search: {e}\")\n", "        return []\n", "\n", "    # --- Step 2: Use an LLM to extract company names from the search results ---\n", "    print(\" extracting company names from search results...\")\n", "\n", "    # The prompt is critical. It tells the LLM to act as an expert and format the output perfectly.\n", "    extraction_prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", \"You are an expert market analyst. Your sole task is to extract company names from a given text.\"),\n", "        (\"human\", \"\"\"Based on the following text, identify 3 prominent companies in the {industry} industry.\n", "\n", "        <text>\n", "        {search_context}\n", "        </text>\n", "\n", "        Instructions:\n", "        1. Return ONLY a comma-separated list of 3 company names.\n", "        2. Do NOT add any introduction, explanation, or conclusion.\n", "        3. Example format: Company A, Company B, Company C\"\"\")\n", "    ])\n", "\n", "    # Initialize the language model\n", "    llm = ChatGroq(model_name='Gemma2-9b-it', api_key=os.getenv('GROQ_API_KEY'))\n", "\n", "    # Create and run the extraction chain\n", "    extraction_chain = extraction_prompt | llm | StrOutputParser()\n", "    company_string = extraction_chain.invoke({\n", "        \"industry\": industry,\n", "        \"search_context\": search_context\n", "    })\n", "\n", "    # --- Step 3: Clean up and return the list ---\n", "    if company_string:\n", "        # Split the string by commas and strip any extra whitespace from each name\n", "        companies = [name.strip() for name in company_string.split(',') if name.strip()]\n", "        return companies\n", "    else:\n", "        return []\n", "\n", "if __name__ == \"__main__\":\n", "    # Load API keys from your .env file\n", "    load_dotenv()\n", "\n", "    print(\"--- Industry Company Finder ---\")\n", "    \n", "    try:\n", "        # Get input from the user\n", "        industry_name = input(\"Enter the industry or domain: \").strip()\n", "\n", "        # Get the list of companies\n", "        company_list = get_companies_in_industry(industry_name)\n", "\n", "        # Print the results\n", "        if company_list:\n", "            print(f\"\\nFound the following prominent companies in the {industry_name} industry:\")\n", "            for company in company_list:\n", "                print(f\"- {company}\")\n", "        else:\n", "            print(f\"\\nCould not find any prominent companies for the {industry_name} industry.\")\n", "\n", "    except ValueError as e:\n", "        print(f\"\\nError: {e}\")\n", "        print(\"Please make sure you have a .env file with TAVILY_API_KEY and GROQ_API_KEY.\")\n", "    except Exception as e:\n", "        print(f\"\\nAn unexpected error occurred: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b1b22005", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai_project_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}