import os
import asyncio
import json
from typing import Dict, List, Any, TypedDict, Optional
from datetime import datetime
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse

# To load environment variables from a .env file
from dotenv import load_dotenv

# LangChain components for interacting with the language model
from langchain_groq import ChatGroq
from langchain_core.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser
from langchain_community.tools.tavily_search import TavilySearchResults

# Tavily for performing the web search (direct client for broad search if needed)
from tavily import TavilyClient

# Google Generative AI for Gemini LLM
import google.generativeai as genai

# Langgraph imports
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

# Configure logging to be less verbose for the backend module
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Re-using your FreeEconomicDataAPI Class ---
class FreeEconomicDataAPI:
    """
    Comprehensive API client for free economic data sources
    Combines World Bank, IMF, FRED, OECD, and other free sources
    """
    def __init__(self, fred_api_key: Optional[str] = None):
        self.fred_api_key = fred_api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Economic-Data-Client/1.0'
        })

    def get_world_bank_data(self, country_code: str, indicator: str,
                           start_year: str = "2010", end_year: str = "2024") -> Dict:
        """Get World Bank economic data"""
        url = f"https://api.worldbank.org/v2/countries/{country_code}/indicators/{indicator}"
        params = {
            'format': 'json',
            'date': f"{start_year}:{end_year}",
            'per_page': 1000
        }
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return {
                'source': 'World Bank',
                'country': country_code,
                'indicator': indicator,
                'data': data[1] if len(data) > 1 else []
            }
        except Exception as e:
            logger.error(f"Error fetching World Bank data for {country_code} ({indicator}): {e}")
            return {'source': 'World Bank', 'error': str(e)}

    def to_dataframe(self, data: Dict) -> Any:
        """Convert World Bank or FRED API response to pandas DataFrame"""
        import pandas as pd
        try:
            source = data.get('source', '').lower()
            if source == 'world bank' or source == 'fred':
                df = pd.DataFrame(data.get('data', []))
                if not df.empty and 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    if 'value' in df.columns:
                        df['value'] = pd.to_numeric(df['value'], errors='coerce')
                    df = df.sort_values('date', ascending=False)
                return df
            return pd.DataFrame(data)
        except Exception as e:
            logger.error(f"Error converting to DataFrame: {e}")
            return pd.DataFrame()

# --- Langgraph Setup for Economic Insights ---
class EconomicAgentState(TypedDict):
    api_client: FreeEconomicDataAPI
    country_code: str
    gdp_growth_df: Optional[Any]
    inflation_df: Optional[Any]
    gdp_growth_str: str
    inflation_str: str
    analysis_prompt: str
    llm_insights: str

def fetch_economic_data(state: EconomicAgentState) -> Dict:
    api_client = state["api_client"]
    country_code = state["country_code"]
    gdp_data_raw = api_client.get_world_bank_data(country_code, 'NY.GDP.MKTP.KD.ZG', start_year="2010", end_year="2024")
    inflation_data_raw = api_client.get_world_bank_data(country_code, 'FP.CPI.TOTL.ZG', start_year="2010", end_year="2024")
    gdp_df = api_client.to_dataframe(gdp_data_raw).dropna(subset=['value'])
    inflation_df = api_client.to_dataframe(inflation_data_raw).dropna(subset=['value'])
    return {"gdp_growth_df": gdp_df, "inflation_df": inflation_df}

def format_data_for_llm(state: EconomicAgentState) -> Dict:
    gdp_df = state["gdp_growth_df"]
    inflation_df = state["inflation_df"]
    gdp_str = gdp_df[['date', 'value']].to_markdown(index=False) if not gdp_df.empty else "No GDP Growth data available."
    inflation_str = inflation_df[['date', 'value']].to_markdown(index=False) if not inflation_df.empty else "No Inflation data available."
    return {"gdp_growth_str": gdp_str, "inflation_str": inflation_str}

def prepare_analysis_prompt(state: EconomicAgentState) -> dict:
    gdp_data = state["gdp_growth_str"]
    inflation_data = state["inflation_str"]
    country_code = state["country_code"]
    prompt = (
        f"You are an expert economic analyst. Analyze the economic data for {country_code} "
        "and provide concise, actionable insights on key trends, changes, and implications "
        f"focusing on GDP growth and inflation.\n\n"
        f"--- {country_code} GDP Growth (Annual %) ---\n{gdp_data}\n\n"
        f"--- {country_code} Inflation (Annual %) ---\n{inflation_data}\n\n"
        "Provide insights on:\n"
        "1. Key trends in GDP growth and inflation.\n"
        "2. Notable positive or negative changes and specific years.\n"
        "3. Apparent correlations between GDP and inflation.\n"
        f"4. A brief overall assessment of {country_code}'s recent economic performance and future outlook."
    )
    return {"analysis_prompt": prompt}

def get_llm_economic_insights(state: EconomicAgentState) -> dict:
    llm = ChatGroq(model="meta-llama/llama-4-maverick-17b-128e-instruct", temperature=0.0)
    try:
        response = llm.invoke(state["analysis_prompt"])
        return {"llm_insights": response.content}
    except Exception as e:
        logger.error(f"Error calling Groq API for economic insights: {e}")
        return {"llm_insights": f"Error: Could not retrieve insights: {e}"}

economic_workflow = StateGraph(EconomicAgentState)
economic_workflow.add_node("fetch_data", fetch_economic_data)
economic_workflow.add_node("format_data", format_data_for_llm)
economic_workflow.add_node("prepare_prompt", prepare_analysis_prompt)
economic_workflow.add_node("get_insights", get_llm_economic_insights)
economic_workflow.set_entry_point("fetch_data")
economic_workflow.add_edge("fetch_data", "format_data")
economic_workflow.add_edge("format_data", "prepare_prompt")
economic_workflow.add_edge("prepare_prompt", "get_insights")
economic_workflow.add_edge("get_insights", END)
economic_app = economic_workflow.compile()


# --- IndustryTrendsAgent ---
class AgentStateIndustry(TypedDict):
    messages: List[BaseMessage]
    current_industry: str
    industry_trends: Dict[str, Any]
    completed_industries: List[str]
    final_report: str

class IndustryTrendsAgent:
    def __init__(self, groq_api_key: str, tavily_api_key: str, target_industry: str):
        self.groq_api_key = groq_api_key
        self.tavily_api_key = tavily_api_key
        self.target_industry = target_industry
        self.llm = ChatGroq(temperature=0.1, model_name="meta-llama/llama-4-maverick-17b-128e-instruct", groq_api_key=groq_api_key, max_tokens=4000)
        self.search_tool = TavilySearchResults(api_key=tavily_api_key, max_results=5)
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        def route_next_industry(state: AgentStateIndustry) -> str:
            return "generate_final_report" if self.target_industry in state.get("completed_industries", []) else "analyze_industry"

        async def analyze_industry_node(state: AgentStateIndustry) -> AgentStateIndustry:
            current_industry = self.target_industry
            search_query = f"{current_industry} industry mega trends 2024 2025 technology innovation market forecast"
            search_results = await self.search_tool.ainvoke(search_query)
            analysis_prompt = ChatPromptTemplate.from_messages([
                ("system", "You are an expert industry analyst. Analyze the search results to identify the top 3-5 mega trends for the specified industry. For each trend, provide: Trend Name, Description, Impact Level (High/Medium/Low), Timeline (Near/Medium/Long-term), Key Drivers, and Potential Disruptions."),
                ("human", "Industry: {industry}\n\nSearch Results:\n{search_results}\n\nPlease provide a comprehensive mega trends analysis.")
            ])
            chain = analysis_prompt | self.llm
            response = await chain.ainvoke({"industry": current_industry, "search_results": json.dumps(search_results, indent=2)})
            industry_trends = state.get("industry_trends", {})
            industry_trends[current_industry] = {"analysis": response.content, "search_data": search_results}
            completed_industries = state.get("completed_industries", []) + [current_industry]
            return {**state, "industry_trends": industry_trends, "completed_industries": completed_industries}

        async def generate_final_report_node(state: AgentStateIndustry) -> AgentStateIndustry:
            summary_prompt = ChatPromptTemplate.from_messages([
                ("system", "You are an executive consultant. Create a professional, structured executive mega trends report based on the provided analysis. Include: 1. EXECUTIVE SUMMARY, 2. INDUSTRY MEGA TRENDS, 3. STRATEGIC RECOMMENDATIONS, 4. TIMELINE OUTLOOK."),
                ("human", "Based on the industry analysis below, create an executive mega trends report:\n\n{industry_analyses}")
            ])
            industry_analyses = "\n".join([f"=== {industry.upper()} ===\n{data['analysis']}" for industry, data in state["industry_trends"].items()])
            chain = summary_prompt | self.llm
            final_report = await chain.ainvoke({"industry_analyses": industry_analyses})
            return {**state, "final_report": final_report.content}

        workflow = StateGraph(AgentStateIndustry)
        workflow.add_node("analyze_industry", analyze_industry_node)
        workflow.add_node("generate_final_report", generate_final_report_node)
        workflow.set_entry_point("analyze_industry")
        workflow.add_conditional_edges("analyze_industry", route_next_industry, {"analyze_industry": "analyze_industry", "generate_final_report": "generate_final_report"})
        workflow.add_edge("generate_final_report", END)
        return workflow.compile()

    async def analyze_industry_single(self) -> Dict[str, Any]:
        initial_state: AgentStateIndustry = {"messages": [], "current_industry": self.target_industry, "industry_trends": {}, "completed_industries": [], "final_report": ""}
        return await self.graph.ainvoke(initial_state)

# --- List of Industries ---
ALL_INDUSTRIES = [
    "5G and Wireless Equipment", "Aerospace", "Appliances", "Automation & Control", "Automotive", "Autosport",
    "Commercial & Industrial Vehicles", "Connected Home", "Data Centers & Artificial Intelligence", "Defense & Military",
    "Energy Solutions", "E-mobility", "Industrial Machinery", "Intelligent Buildings & Smart Cities",
    "IoT Connectivity", "Medical Technologies", "Oil & Gas / Marine", "Personal Electronics & Wearable Technology",
    "Rail", "Sensor Applications", "Space"
]

# --- Company & Competitor Analysis Functions ---
async def get_primary_company_in_industry_async(industry: str, groq_api_key: str, tavily_api_key: str) -> Optional[str]:
    """Finds one prominent company in an industry."""
    search_tool = TavilySearchResults(api_key=tavily_api_key, max_results=3)
    search_query = f"leading company in the {industry} industry by market share"
    try:
        search_results = await search_tool.ainvoke(search_query)
        search_context = "\n".join([result.get('content', '') for result in search_results])
        if not search_context.strip(): return None
        extraction_prompt = ChatPromptTemplate.from_messages([
            ("system", "You are an expert market analyst. Extract a single company name from the text."),
            ("human", "Based on the text, identify the single most prominent company in the {industry} industry.\n\n<text>{search_context}</text>\n\nInstructions: Return ONLY the company name.")
        ])
        llm = ChatGroq(model_name='meta-llama/llama-4-maverick-17b-128e-instruct', api_key=groq_api_key)
        chain = extraction_prompt | llm | StrOutputParser()
        company_name = await chain.ainvoke({"industry": industry, "search_context": search_context})
        return company_name.strip() if company_name else None
    except Exception as e:
        logger.error(f"Error finding primary company in {industry}: {e}")
        return None

async def get_competitors_async(company: str, industry: str, groq_api_key: str, tavily_api_key: str) -> List[str]:
    """Finds competitors for a given company."""
    search_tool = TavilySearchResults(api_key=tavily_api_key, max_results=3)
    search_query = f"key direct competitors of {company} in the {industry} market"
    try:
        search_results = await search_tool.ainvoke(search_query)
        search_context = "\n".join([result.get('content', '') for result in search_results])
        if not search_context.strip(): return []
        extraction_prompt = ChatPromptTemplate.from_messages([
            ("system", "You are an expert market analyst. Extract competitor names from the text."),
            ("human", "Based on the text, identify main competitors of {company}.\n\n<text>{search_context}</text>\n\nInstructions: Return ONLY a comma-separated list of competitor names. Do not include {company}.")
        ])
        llm = ChatGroq(model_name='meta-llama/llama-4-maverick-17b-128e-instruct', api_key=groq_api_key)
        chain = extraction_prompt | llm | StrOutputParser()
        competitor_string = await chain.ainvoke({"company": company, "search_context": search_context})
        return [name.strip() for name in competitor_string.split(',') if name.strip()] if competitor_string else []
    except Exception as e:
        logger.error(f"Error finding competitors for {company}: {e}")
        return []

# --- Web Scraping & Content Generation Functions ---
async def simple_scrape_async(url: str) -> str:
    """Asynchronously scrapes text content from a URL."""
    def _sync_scrape(u):
        try:
            session = requests.Session()
            session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'})
            response = session.get(u, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            for element in soup(['script', 'style', 'header', 'footer', 'nav', 'aside']):
                element.extract()
            return ' '.join(soup.get_text().split())
        except Exception:
            return ""
    return await asyncio.to_thread(_sync_scrape, url)

async def generate_company_update_async(company_name: str, text_content: str, tavily_search_results: List[Dict]) -> str:
    """Uses Gemini LLM to generate a structured company update."""
    max_chars = 30000
    text_content = text_content[:max_chars]
    
    context_string = "\n\n--- Top Search Results for Context ---\n"
    for res in tavily_search_results[:7]:
        context_string += f"- Title: {res.get('title')}, Source: {urlparse(res.get('url')).netloc}\n"

    try:
        model = genai.GenerativeModel('gemini-1.5-flash-latest')
        current_year = datetime.now().year
        prompt = f"""
        You are a financial analyst. Based on the aggregated text and search results for '{company_name}', generate a concise company update.
        
        {context_string}

        Aggregated Scraped Text for detailed analysis:
        ---
        {text_content}
        ---

        Structure your update *exactly* as follows:

        ### {company_name}
        **What’s new (last ~2–6 weeks)**
        * [Brief summary of a recent development]. [Source, e.g., Benzinga, Company IR]
        * [Another recent development]. [Source]

        **Why it matters (insights)**
        1. **[Insight 1]:** Explain the significance of the news.
        2. **[Insight 2]:** ...

        **Watchlist for Q3-Q4 {current_year}:**
        * [Key event or trend to monitor]. [Reason]
        * [Another key item].
        """
        response = await model.generate_content_async(prompt)
        return response.text
    except Exception as e:
        logger.error(f"Error generating company update for {company_name}: {e}")
        return f"### {company_name}\n\n**Error:** Could not generate update. {e}"

async def get_single_company_insights_async(company_name: str, tavily_api_key: str, gemini_api_key: str) -> Dict[str, Any]:
    """Workflow to get insights for a single company."""
    genai.configure(api_key=gemini_api_key)
    tavily_client = TavilyClient(api_key=tavily_api_key)
    
    try:
        search_query = f"{company_name} latest news OR financial update OR investor announcements"
        response = tavily_client.search(search_query, search_depth="advanced", max_results=15)
        search_results_raw = response.get('results', [])
        if not search_results_raw:
            return {"company_name": company_name, "status": "failed", "error": "No search results."}
    except Exception as e:
        return {"company_name": company_name, "status": "failed", "error": f"Tavily search error: {e}"}

    potential_sources = [res['url'] for res in search_results_raw if res.get('url')]
    if not potential_sources:
        return {"company_name": company_name, "status": "failed", "error": "No URLs found."}

    scrape_tasks = [simple_scrape_async(url) for url in potential_sources]
    scraped_texts = await asyncio.gather(*scrape_tasks)
    full_scraped_corpus = " ".join(filter(None, scraped_texts))

    if not full_scraped_corpus or len(full_scraped_corpus) < 300:
        return {"company_name": company_name, "status": "failed", "error": "Not enough scraped content."}

    update_report = await generate_company_update_async(company_name, full_scraped_corpus, search_results_raw)
    
    return {"company_name": company_name, "status": "completed", "update_report": update_report}

# --- Main Orchestrator ---
async def orchestrate_full_market_report(
    country_code: str,
    industries_list: List[str],
    fred_api_key: str,
    groq_api_key: str,
    tavily_api_key: str,
    gemini_api_key: str,
    progress_callback: Optional[callable] = None
) -> Dict[str, Any]:
    """
    Orchestrates the full market report generation with progress updates.
    """
    load_dotenv()
    full_report_data: Dict[str, Any] = {
        "report_timestamp": datetime.now().isoformat(),
        "country_economic_overview": {}, "industry_analysis": {}, "company_insights": {}
    }
    total_steps = 1 + len(industries_list) * 2
    current_step = 0

    # PART 1: Country Economic Overview
    current_step += 1
    if progress_callback: progress_callback(f"({current_step}/{total_steps}) Analyzing economic overview for {country_code}...")
    try:
        economic_api_client = FreeEconomicDataAPI(fred_api_key=fred_api_key)
        initial_state = {"api_client": economic_api_client, "country_code": country_code}
        final_economic_state = await economic_app.ainvoke(initial_state)
        full_report_data["country_economic_overview"] = final_economic_state
    except Exception as e:
        full_report_data["country_economic_overview"] = {"status": "failed", "error": str(e)}

    # PART 2 & 3: Industry and Company Analysis
    industry_reports = {}
    company_insights_all = {}
    for i, industry_name in enumerate(industries_list):
        # Industry Trends
        current_step += 1
        if progress_callback: progress_callback(f"({current_step}/{total_steps}) Analyzing mega trends for: {industry_name}...")
        try:
            agent = IndustryTrendsAgent(groq_api_key, tavily_api_key, industry_name)
            trends_results = await agent.analyze_industry_single()
            industry_reports[industry_name] = {
                "executive_report_summary": trends_results.get("final_report"),
                "status": "completed"
            }
        except Exception as e:
            industry_reports[industry_name] = {"status": "failed", "error": str(e)}

        # Company Insights
        current_step += 1
        if progress_callback: progress_callback(f"({current_step}/{total_steps}) Analyzing companies in: {industry_name}...")
        company_insights_all[industry_name] = {}
        try:
            primary_company = await get_primary_company_in_industry_async(industry_name, groq_api_key, tavily_api_key)
            if not primary_company:
                company_insights_all[industry_name] = {"status": "no_primary_company_found"}
                continue
            
            competitors = await get_competitors_async(primary_company, industry_name, groq_api_key, tavily_api_key)
            companies_to_analyze = [primary_company] + competitors
            
            tasks = [get_single_company_insights_async(comp, tavily_api_key, gemini_api_key) for comp in companies_to_analyze]
            company_results = await asyncio.gather(*tasks)
            
            for result in company_results:
                company_insights_all[industry_name][result["company_name"]] = result
        except Exception as e:
            company_insights_all[industry_name] = {"status": "failed", "error": str(e)}

    full_report_data["industry_analysis"] = {"individual_industry_reports": industry_reports}
    full_report_data["company_insights"] = company_insights_all

    if progress_callback: progress_callback("Report generation complete!")
    return full_report_data