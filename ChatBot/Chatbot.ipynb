# %%
import os
import json
from typing import Any, Dict, List, Optional, TypedDict, Literal
from dotenv import load_dotenv

# pydantic for data validation
from pydantic import BaseModel, Field

# LangChain and LangGraph components
from langgraph.graph import StateGraph, START, END
from langchain_groq import <PERSON>tGro<PERSON>
from langchain_core.messages import SystemMessage, HumanMessage, ToolMessage, AIMessage
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema.output_parser import StrOutputParser
from langchain_core.tools import tool

# Web search
from tavily import TavilyClient

# Financial data
import yfinance as yf
import pandas as pd

# Visualization (for generating plot JSON)
import plotly.graph_objects as go
from curl_cffi import requests
import logging

# %%
# --- CONFIGURATION ---

# Load environment variables from .env file
load_dotenv()

# Initialize the language model (Groq's Gemma-2 9B)
llm = ChatGroq(model_name='Gemma2-9b-it', api_key=os.getenv('GROQ_API_KEY'))

# Constants to control the depth of the research loops
MAX_PRIMARY_RESEARCH_LOOPS = 3
MAX_COMPETITOR_RESEARCH_LOOPS = 2 # A mini-loop for each competitor

# Use a session for yfinance to improve performance
session = requests.Session(impersonate="chrome")


# %%
# --- AGENT STATE DEFINITION ---

class State(TypedDict):
    """
    Defines the shared state that flows through the agent's graph.
    """
    # Core routing and conversation management
    route: Literal['Web_query', 'Normal_query', 'Financial_Analysis', 'Plot_Graph', 'Market_Report']
    research_topic: str
    messages: List[Any]

    # State for the intensive research loops
    search_query: str
    web_research_results: str
    running_summary: str
    research_loop_count: int

    # State for plotting financial charts
    plot_type: Optional[str]
    ticker: Optional[str]
    plot_json: Optional[str]

    # State for the market report context
    company: str
    industry: str
    competitors: List[str]
    current_competitor_index: int


# %%
# --- HELPER FUNCTIONS & TOOLS ---

def fetch_stock_data(ticker, period="1y"):
    """Fetches historical stock data for a given ticker."""
    stock = yf.Ticker(ticker, session=session)
    return stock.history(period=period)

def plot_candles_stick(df, title=""):
    """Generates a candlestick chart from a DataFrame."""
    fig = go.Figure(data=[go.Candlestick(x=df.index, open=df['Open'], high=df['High'], low=df['Low'], close=df['Close'])])
    fig.update_layout(title=title)
    return fig

def tavily_search(query: str, max_results: int = 4) -> str:
    """Performs a web search using Tavily and returns formatted results."""
    api_key = os.environ.get('TAVILY_API_KEY')
    if not api_key:
        raise ValueError("TAVILY_API_KEY environment variable not set.")
    tavily_client = TavilyClient(api_key=api_key)
    try:
        results = tavily_client.search(query, max_results=max_results)['results']
        return "\n\n".join([f"Source: {res['title']}\nURL: {res['url']}\nContent: {res['content']}" for res in results])
    except Exception as e:
        return f"Error during web search: {e}"

@tool
def last_close_price(ticker: str) -> float:
    """Returns the last closing price for a given stock ticker."""
    ticker_obj = yf.Ticker(ticker, session=session)
    info = ticker_obj.info
    return info.get('previousClose', 'N/A')

@tool
def total_debt(ticker: str) -> float:
    """Returns the total debt for a given stock ticker."""
    ticker_obj = yf.Ticker(ticker, session=session)
    info = ticker_obj.info
    return info.get('totalDebt', 'N/A')

finance_tools = [last_close_price, total_debt]
finance_tool_map = {t.name: t for t in finance_tools}


# %%
# --- AGENT ROUTING LOGIC ---

class Route_First_Step(BaseModel):
    """The initial routing model to decide the agent's path."""
    step: Literal['Market_Report', 'Web_query', 'Normal_query', 'Financial_Analysis', 'Plot_Graph'] = Field(None)
    company: Optional[str] = Field(None)
    industry: Optional[str] = Field(None)


# %%
# --- AGENT NODES (CORE FUNCTIONS) ---

# --- Nodes for Simple Q&A, Financial Tools, and Plotting ---
def web_research_node(state: State) -> dict:
    """Node for performing a web search, used by both research loops."""
    print(f"--- Performing Web Research on: {state['search_query']} ---")
    results = tavily_search(state['search_query'])
    current_count = state.get('research_loop_count', 0)
    return {"web_research_results": results, "research_loop_count": current_count + 1}

def answer_normal_query(state: State) -> dict:
    """Node for answering a general knowledge question."""
    prompt = ChatPromptTemplate.from_messages([("system", "You are a helpful financial assistant..."), ("human", "{query}")])
    chain = prompt | llm | StrOutputParser()
    return {"running_summary": chain.invoke({"query": state['research_topic']})}

def call_financial_llm(state: State) -> dict:
    """Node that decides which financial tool to use."""
    messages = [SystemMessage(content="You are a financial analyst...")] + state['messages']
    llm_with_tools = llm.bind_tools(finance_tools, tool_choice='auto')
    message = llm_with_tools.invoke(messages)
    return {'messages': state['messages'] + [message]}

def exists_action(state: State) -> bool:
    """Checks if the LLM decided to call a tool."""
    return len(state['messages'][-1].tool_calls) > 0

def take_action(state: State) -> dict:
    """Node for executing a financial tool."""
    tool_calls = state['messages'][-1].tool_calls
    tool_results = [ToolMessage(tool_call_id=t['id'], name=t['name'], content=str(finance_tool_map[t['name']].invoke(t['args']))) for t in tool_calls]
    summary = "\n".join([f"Data for {res.name}: {res.content}" for res in tool_results])
    return {'messages': state['messages'] + tool_results, 'running_summary': summary}

def parse_plot_query(state: State) -> dict:
    """Node to parse the user's request for a plot."""
    words = state["research_topic"].lower().split()
    ticker = words[-1].upper()
    plot_type = "candlestick" if "candlestick" in state["research_topic"].lower() else None
    return {"plot_type": plot_type, "ticker": ticker}

def generate_plot(state: State) -> dict:
    """Node to generate the plot JSON from financial data."""
    try:
        if state.get("plot_type") == "candlestick":
            df = fetch_stock_data(state["ticker"])
            fig = plot_candles_stick(df, title=f"{state['ticker']} Candlestick Chart")
            return {"plot_json": fig.to_json(), "running_summary": f"Generated candlestick plot for {state['ticker']}."}
        else:
            return {"running_summary": "Plot type not supported or not specified."}
    except Exception as e:
        return {"running_summary": f"Error generating plot: {e}"}

# --- Nodes for the Intensive Market Report ---

def start_report_generation(state: State) -> dict:
    """Kicks off the report by setting the first research query."""
    print("--- Starting Intensive Market Report Generation (Phase 1: Primary Company) ---")
    initial_query = f"Initial overview of {state['company']} in the {state['industry']} industry, including key players, market size, and SWOT analysis."
    return {"search_query": initial_query, "running_summary": "", "research_loop_count": 0}

def summarize_and_update(state: State) -> dict:
    """Summarizes new findings and integrates them into the running summary."""
    print("--- Summarizing and Updating Report ---")
    summarizer_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a market research analyst... Integrate new findings into the existing summary..."),
        ("human", "<Existing_Summary>\n{existing_summary}\n</Existing_Summary>\n\n<New_Information>\n{new_information}\n</New_Information>\n\nProvide the updated, integrated summary.")
    ])
    chain = summarizer_prompt | llm | StrOutputParser()
    updated_summary = chain.invoke({"existing_summary": state['running_summary'], "new_information": state['web_research_results']})
    return {"running_summary": updated_summary}

def reflect_on_summary(state: State) -> dict:
    """Analyzes the current summary to find gaps and generate the next research query."""
    print("--- Reflecting on Research and Identifying Gaps ---")
    reflection_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a senior market analyst... Find the single most critical knowledge gap and formulate a targeted web search query to fill it..."),
        ("human", "<Research_Summary>\n{summary}\n</Research_Summary>\n\nGenerate the next single web search query to deepen this analysis.")
    ])
    chain = reflection_prompt | llm | StrOutputParser()
    next_query = chain.invoke({"summary": state['running_summary']})
    return {"search_query": next_query}

def identify_competitors(state: State) -> dict:
    """Parses the main summary to extract competitor names."""
    print("--- Identifying Competitors from Research ---")
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are an expert at extracting information. From the text provided, identify the top 3-4 direct competitors. Return their names as a simple comma-separated list. Example: Apple, Samsung, Xiaomi"),
        ("human", "Research Summary:\n\n{summary}")
    ])
    chain = prompt | llm | StrOutputParser()
    competitor_string = chain.invoke({"summary": state['running_summary']})
    # Filter out the primary company from the competitor list if it appears
    competitors = [c.strip() for c in competitor_string.split(',') if c.strip() and c.strip().lower() != state['company'].lower()]
    print(f"Found competitors: {competitors}")
    return {"competitors": competitors, "current_competitor_index": 0}

def manage_competitor_loop(state: State) -> dict:
    """Manages the flow of the competitor research phase."""
    current_index = state.get('current_competitor_index', 0)
    competitors = state.get('competitors', [])

    if not competitors or current_index >= len(competitors):
        print("--- All competitor research complete. ---")
        return {"route": "compile_final_report"}
    else:
        competitor_name = competitors[current_index]
        print(f"--- Starting research on Competitor {current_index + 1}/{len(competitors)}: {competitor_name} ---")
        initial_competitor_query = f"Detailed analysis of {competitor_name}, focusing on its market position, recent news, and strategy against {state['company']}."
        return {
            "search_query": initial_competitor_query,
            "current_competitor_index": current_index + 1,
            "research_loop_count": 0,
            "route": "start_next_competitor_research"
        }

def compile_final_report(state: State) -> dict:
    """Formats the final running summary into a polished report."""
    print("--- Compiling Final Polished Report ---")
    compiler_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a professional market analyst. Format the research notes into a final, polished report. The notes contain info on a primary company AND its competitors. Structure the report with a clear 'Executive Summary', a main section for the primary company, and a detailed 'Competitive Landscape' section for the competitors. Use markdown for clear headings."),
        ("human", "<Research_Notes>\n{notes}\n</Research_Notes>\n\nGenerate the final, well-structured market report.")
    ])
    chain = compiler_prompt | llm | StrOutputParser()
    final_report = chain.invoke({"notes": state['running_summary']})
    return {"running_summary": final_report}

# --- NEW: A single, powerful routing function to replace the previous ones ---
def decide_next_step_after_summary(state: State) -> str:
    """
    After summarizing, this router decides the next step based on the research phase and loop count.
    """
    # If competitors list exists, we are in Phase 2 (Competitor Analysis)
    if "competitors" in state and state["competitors"]:
        print(f"--- Competitor Research Loop, Count: {state['research_loop_count']} ---")
        if state['research_loop_count'] >= MAX_COMPETITOR_RESEARCH_LOOPS:
            print("--- Mini-loop for this competitor complete. Moving to next. ---")
            return "manage_competitor_loop"
        else:
            return "reflect"
    # Otherwise, we are in Phase 1 (Primary Company Analysis)
    else:
        print(f"--- Primary Research Loop, Count: {state['research_loop_count']} ---")
        if state['research_loop_count'] >= MAX_PRIMARY_RESEARCH_LOOPS:
            print("--- Primary research complete. Proceeding to competitor analysis. ---")
            return "identify_competitors"
        else:
            return "reflect"

# %%
# --- GRAPH DEFINITION & WIRING ---

def call_route_first_step(state: State):
    """The main router node that directs the agent's path."""
    print("---Routing Initial Query---")
    router_llm = llm.with_structured_output(Route_First_Step)
    routing = router_llm.invoke(state["research_topic"])
    print(f"Routing result: {routing.step}")
    if routing.step == 'Market_Report':
        return {"route": routing.step, "company": routing.company, "industry": routing.industry}
    return {"route": routing.step}

def build_agent():
    """Builds and compiles the agent's state graph."""
    workflow = StateGraph(State)

    # Add all nodes
    workflow.add_node("route_first_step", call_route_first_step)
    workflow.add_node("answer_normal_query", answer_normal_query)
    workflow.add_node('call_llm', call_financial_llm)
    workflow.add_node('take_action', take_action)
    workflow.add_node("parse_plot_query", parse_plot_query)
    workflow.add_node("generate_plot", generate_plot)
    workflow.add_node("start_report", start_report_generation)
    workflow.add_node("web_research", web_research_node)
    workflow.add_node("summarize", summarize_and_update)
    workflow.add_node("reflect", reflect_on_summary)
    workflow.add_node("identify_competitors", identify_competitors)
    workflow.add_node("manage_competitor_loop", manage_competitor_loop)
    workflow.add_node("compile_report", compile_final_report)

    # Define connections
    workflow.add_edge(START, "route_first_step")

    # Conditional routing from the first step
    workflow.add_conditional_edges("route_first_step", lambda x: x.get('route', ''), {
        'Market_Report': 'start_report',
        'Normal_query': 'answer_normal_query',
        'Financial_Analysis': 'call_llm',
        'Plot_Graph': 'parse_plot_query'
    })

    # Research Workflow
    workflow.add_edge("start_report", "web_research")
    workflow.add_edge("web_research", "summarize")
    workflow.add_edge("reflect", "web_research") # The single loop point

    # --- Use the new, unified conditional router after summarizing ---
    workflow.add_conditional_edges(
        "summarize",
        decide_next_step_after_summary,
        {
            "reflect": "reflect",
            "identify_competitors": "identify_competitors",
            "manage_competitor_loop": "manage_competitor_loop"
        }
    )

    # Transition to and Management of Phase 2
    workflow.add_edge("identify_competitors", "manage_competitor_loop")
    workflow.add_conditional_edges("manage_competitor_loop", lambda x: x['route'], {
        "start_next_competitor_research": "web_research",
        "compile_final_report": "compile_report"
    })

    # Endpoints
    workflow.add_edge("answer_normal_query", END)
    workflow.add_edge("compile_report", END)
    workflow.add_edge("parse_plot_query", "generate_plot")
    workflow.add_edge("generate_plot", END)
    workflow.add_conditional_edges("call_llm", exists_action, {True: "take_action", False: END})
    workflow.add_edge("take_action", END)
    
    return workflow.compile()


# %%
# --- CHATBOT CLASS & MAIN EXECUTION ---

class FinancialChatBot:
    """A class to manage the conversation and state."""
    def __init__(self):
        self.model = build_agent()
        self.conversation_history = []
        self.context_messages = []

    def _update_context(self, user_input: str, bot_response: str):
        self.context_messages.append(HumanMessage(content=user_input))
        self.context_messages.append(AIMessage(content=bot_response))
        if len(self.context_messages) > 6:
            self.context_messages = self.context_messages[-6:]

    def chat(self, user_input: str, company: str = None, industry: str = None) -> dict:
        """Main method to interact with the agent."""
        self.conversation_history.append(f"👤 User: {user_input}")

        initial_state = {
            "research_topic": user_input,
            "messages": self.context_messages + [HumanMessage(content=user_input)],
            "company": company,
            "industry": industry,
        }

        try:
            final_state = self.model.invoke(initial_state)
            text_response = final_state.get('running_summary', 'I could not find an answer.')
            plot_json = final_state.get('plot_json')
            self._update_context(user_input, text_response)
            self.conversation_history.append(f"🤖 Assistant: {text_response}")
            return {"text": text_response, "plot": plot_json}
        except Exception as e:
            import traceback
            error_message = f"An error occurred: {e}\n{traceback.format_exc()}"
            self.conversation_history.append(f"🤖 Assistant: {error_message}")
            return {"text": error_message, "plot": None}

def main():
    """The main function to run the chatbot in the console."""
    chatbot = FinancialChatBot()
    print("Welcome to the Advanced Financial Analyst Assistant!")
    print("You can ask for a detailed 'report' or start a general chat.")
    print("Type 'quit' to exit.")

    mode = input("Type 'report' to generate a market report, or press Enter to start a chat: ").strip().lower()

    if mode == 'report':
        company = input("Please enter the company name: ").strip()
        industry = input("Please enter the industry: ").strip()
        print("\nGenerating your intensive market report. This will involve multiple research steps and may take a few minutes...")
        query = f"Generate an intensive market report for {company} in the {industry} industry."
        response = chatbot.chat(query, company=company, industry=industry)

        print("\n\n" + "="*60)
        print("            Intensive Market Research Report")
        print("="*60 + "\n")
        print(response['text'])
        print("\nYou can now ask follow-up questions about the report or anything else.")

    while True:
        user_input = input("\n👤 You: ").strip()
        if user_input.lower() == 'quit':
            print("\nGoodbye!")
            break
        response = chatbot.chat(user_input)
        print(f"\n🤖 Assistant:\n{response['text']}")
        if response['plot']:
            print("\n[A plot has been generated. In a graphical interface, it would be displayed here.]")


if __name__ == "__main__":
    main()

import os
from typing import List

# To load environment variables from a .env file
from dotenv import load_dotenv

# LangChain components for interacting with the language model
from langchain_groq import ChatGroq
from langchain_core.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser

# Tavily for performing the web search
from tavily import TavilyClient

def get_competitors(company: str, industry: str) -> List[str]:
    """
    Finds and returns a list of competitors for a given company and industry.

    This function performs two main steps:
    1.  Uses the Tavily search API to find articles and text about the
        company's competitive landscape.
    2.  Feeds this text to a language model (LLM) with a specific prompt
        to extract only the names of the competitors.

    Args:
        company: The name of the company to research.
        industry: The industry or domain of the company.

    Returns:
        A list of strings, where each string is the name of a competitor.
        Returns an empty list if no competitors are found.
    """
    print(f"\n researching competitors for {company} in the {industry} industry...")

    # --- Step 1: Perform a targeted web search ---
    print(" searching the web for market data...")
    search_query = f"key direct competitors of {company} in the {industry} market"
    
    # Initialize the Tavily client and perform the search
    api_key = os.environ.get('TAVILY_API_KEY')
    if not api_key:
        raise ValueError("TAVILY_API_KEY environment variable not set.")
    tavily_client = TavilyClient(api_key=api_key)
    
    try:
        # We only need 2-3 good sources to find the top competitors
        search_results = tavily_client.search(query=search_query, max_results=3)['results']
        # Combine the content of the search results into a single string
        search_context = "\n\n".join([result['content'] for result in search_results])
    except Exception as e:
        print(f"Error during web search: {e}")
        return []

    # --- Step 2: Use an LLM to extract competitor names from the search results ---
    print(" extracting competitor names from search results...")

    # The prompt is critical. It tells the LLM to act as an expert and format the output perfectly.
    extraction_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are an expert market analyst. Your sole task is to extract company names from a given text."),
        ("human", """Based on the following text, identify the main competitors of {company}.

        <text>
        {search_context}
        </text>

        Instructions:
        1. Return ONLY a comma-separated list of the competitor names.
        2. Do NOT include the original company, '{company}', in the list.
        3. Do NOT add any introduction, explanation, or conclusion.
        4. Example format: Competitor A, Competitor B, Competitor C""")
    ])

    # Initialize the language model
    llm = ChatGroq(model_name='Gemma2-9b-it', api_key=os.getenv('GROQ_API_KEY'))

    # Create and run the extraction chain
    extraction_chain = extraction_prompt | llm | StrOutputParser()
    competitor_string = extraction_chain.invoke({
        "company": company,
        "search_context": search_context
    })

    # --- Step 3: Clean up and return the list ---
    if competitor_string:
        # Split the string by commas and strip any extra whitespace from each name
        competitors = [name.strip() for name in competitor_string.split(',') if name.strip()]
        return competitors
    else:
        return []

if __name__ == "__main__":
    # Load API keys from your .env file
    load_dotenv()

    print("--- Competitor Finder ---")
    
    try:
        # Get input from the user
        company_name = input("Enter the company name: ").strip()
        industry_name = input("Enter the industry or domain: ").strip()

        # Get the list of competitors
        competitor_list = get_competitors(company_name, industry_name)

        # Print the results
        if competitor_list:
            print(f"\nFound the following competitors for {company_name}:")
            for competitor in competitor_list:
                print(f"- {competitor}")
        else:
            print(f"\nCould not find any direct competitors for {company_name} in the specified domain.")

    except ValueError as e:
        print(f"\nError: {e}")
        print("Please make sure you have a .env file with TAVILY_API_KEY and GROQ_API_KEY.")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")

import os
import requests
from bs4 import BeautifulSoup
from typing import List, Dict, Any

# To load environment variables from a .env file
from dotenv import load_dotenv

# LangChain components for interacting with the language model
from langchain_groq import ChatGroq
from langchain_core.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser

# Tavily for performing web searches
from tavily import TavilyClient

# --- LLM and API Client Initialization ---
load_dotenv()
# Initialize the language model once to be reused
llm = ChatGroq(model_name='meta-llama/llama-4-scout-17b-16e-instruct', api_key=os.getenv('GROQ_API_KEY'))
# Initialize the Tavily client once
tavily_client = TavilyClient(api_key=os.getenv('TAVILY_API_KEY'))


def get_competitors(company: str, industry: str) -> List[str]:
    """Finds and returns a list of competitors for a given company and industry."""
    print(f"\n researching competitors for {company} in the {industry} industry...")
    search_query = f"key direct competitors of {company} in the {industry} market"
    try:
        search_results = tavily_client.search(query=search_query, max_results=3)['results']
        search_context = "\n\n".join([result['content'] for result in search_results])
    except Exception as e:
        print(f"Error during competitor search: {e}")
        return []

    print(" extracting competitor names from search results...")
    extraction_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are an expert market analyst..."),
        ("human", "Based on the text about {company}, return a comma-separated list of its main competitors. Text: <text>{search_context}</text>")
    ])
    extraction_chain = extraction_prompt | llm | StrOutputParser()
    competitor_string = extraction_chain.invoke({"company": company, "search_context": search_context})
    
    if competitor_string:
        competitors = [name.strip() for name in competitor_string.split(',') if name.strip()]
        return competitors
    return []


def research_competitor(competitor_name: str) -> Dict[str, Any]:
    """
    Gathers intelligence on a single competitor by scraping their website and finding recent news.
    """
    print(f"\n--- Researching Competitor: {competitor_name} ---")
    
    # --- Step 1: Find the official website URL ---
    print(f" finding official website for {competitor_name}...")
    try:
        search_results = tavily_client.search(query=f"official website of {competitor_name}", max_results=1)['results']
        if not search_results:
            print(f" could not find a website for {competitor_name}.")
            return {"website_summary": "No website found.", "recent_news": "N/A"}
        website_url = search_results[0]['url']
        print(f" website found: {website_url}")
    except Exception as e:
        print(f" error finding website for {competitor_name}: {e}")
        return {"website_summary": f"Error finding website: {e}", "recent_news": "N/A"}

    # --- Step 2: Scrape the website and summarize its content ---
    website_summary = "Could not scrape or summarize website."
    try:
        print(f" scraping content from {website_url}...")
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
        response = requests.get(website_url, headers=headers, timeout=10)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Get all text from the body and clean it up
        raw_text = soup.body.get_text(separator=' ', strip=True)
        # Truncate to avoid excessive token usage for the summary
        truncated_text = raw_text[:4000]

        print(" summarizing website content...")
        summary_prompt = ChatPromptTemplate.from_messages([
            ("system", "You are an expert at summarizing website content for business analysis."),
            ("human", "Summarize the following text from the company's website to understand their mission, key products, and target audience. Text: <text>{website_text}</text>")
        ])
        summary_chain = summary_prompt | llm | StrOutputParser()
        website_summary = summary_chain.invoke({"website_text": truncated_text})

    except Exception as e:
        print(f" error scraping or summarizing website: {e}")
        website_summary = f"Failed to process website content due to an error: {e}"

    # --- Step 3: Find recent news about the competitor ---
    print(f" gathering recent news about {competitor_name}...")
    recent_news = "No recent news found."
    try:
        news_results = tavily_client.search(query=f"latest news, product launches, and financial performance for {competitor_name}", max_results=3)['results']
        if news_results:
            news_context = "\n\n".join([res['content'] for res in news_results])
            news_summary_prompt = ChatPromptTemplate.from_messages([
                ("system", "You are a financial news analyst."),
                ("human", "Summarize the key points from the following news articles about {competitor_name}. Focus on performance, strategy, and new products. Articles: <articles>{news_articles}</articles>")
            ])
            news_summary_chain = news_summary_prompt | llm | StrOutputParser()
            recent_news = news_summary_chain.invoke({"competitor_name": competitor_name, "news_articles": news_context})
    except Exception as e:
        print(f" error gathering news: {e}")
        recent_news = f"Failed to gather news due to an error: {e}"

    return {
        "website_summary": website_summary,
        "recent_news": recent_news
    }


def generate_report(primary_company: str, industry: str, all_competitor_data: Dict[str, Any]) -> str:
    """
    Generates a final comparative analysis report using all the gathered intelligence.
    """
    print("\n--- Generating Final Competitor Analysis Report ---")

    # Format all the collected data into a single string for the prompt
    formatted_data = f"Primary Company: {primary_company}\nIndustry: {industry}\n\n--- Competitor Intelligence Data ---\n\n"
    for name, data in all_competitor_data.items():
        formatted_data += f"Competitor: {name}\n"
        formatted_data += f"Website Summary: {data['website_summary']}\n"
        formatted_data += f"Recent News Summary: {data['recent_news']}\n\n"

    report_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a senior market analyst. Your task is to write a concise and insightful competitive analysis report based ONLY on the data provided."),
        ("human", """
        Please generate a competitive analysis report using the following data:
        <data>
        {formatted_data}
        </data>

        Structure your report as follows:
        1.  **Executive Summary:** A brief, high-level overview of the competitive landscape.
        2.  **Competitor Deep-Dive:** A dedicated section for each competitor. For each one:
            -   Briefly describe their business based on their website summary.
            -   Analyze their recent activities and performance based on the news summary.
        3.  **Comparative Analysis:** Briefly compare the strategic positions and recent activities of the competitors.
        
        Ensure your analysis is objective and strictly derived from the provided text. Do not add any outside information.
        """)
    ])
    
    report_chain = report_prompt | llm | StrOutputParser()
    final_report = report_chain.invoke({"formatted_data": formatted_data})
    
    return final_report


if __name__ == "__main__":
    print("--- Competitor Analysis Agent ---")
    try:
        # Get input from the user
        company_name = input("Enter the primary company name: ").strip()
        industry_name = input("Enter the industry or domain: ").strip()

        # Step 1: Get the list of competitors
        competitor_list = get_competitors(company_name, industry_name)

        if not competitor_list:
            print(f"\nCould not find any direct competitors for {company_name} in the specified domain.")
        else:
            print(f"\nFound competitors: {', '.join(competitor_list)}")
            
            # Step 2: Research each competitor
            all_data = {}
            for competitor in competitor_list:
                all_data[competitor] = research_competitor(competitor)
            
            # Step 3: Generate the final report
            report = generate_report(company_name, industry_name, all_data)

            # Print the results
            print("\n\n" + "="*60)
            print("            Competitive Analysis Report")
            print("="*60 + "\n")
            print(report)

    except ValueError as e:
        print(f"\nError: {e}")
        print("Please make sure you have a .env file with TAVILY_API_KEY and GROQ_API_KEY.")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")

import os
import requests
import pandas as pd
from datetime import datetime, timedelta
import json
import time
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

class FreeEconomicDataAPI:
    """
    Comprehensive API client for free economic data sources
    Combines World Bank, IMF, FRED, OECD, and other free sources
    """
    
    def __init__(self, fred_api_key: Optional[str] = None):
        """
        Initialize the API client
        
        Args:
            fred_api_key: Optional FRED API key (free from https://fred.stlouisfed.org/docs/api/)
        """
        self.fred_api_key = fred_api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Economic-Data-Client/1.0'
        })
    
    # ===========================================
    # WORLD BANK API (Primary Source)
    # ===========================================
    
    def get_world_bank_countries(self) -> List[Dict]:
        """Get list of all countries from World Bank"""
        url = "https://api.worldbank.org/v2/countries"
        params = {'format': 'json', 'per_page': 500}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return data[1] if len(data) > 1 else []
        except Exception as e:
            print(f"Error fetching World Bank countries: {e}")
            return []
    
    def get_world_bank_indicators(self) -> List[Dict]:
        """Get list of all available indicators from World Bank"""
        url = "https://api.worldbank.org/v2/indicators"
        params = {'format': 'json', 'per_page': 1000}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return data[1] if len(data) > 1 else []
        except Exception as e:
            print(f"Error fetching World Bank indicators: {e}")
            return []
    
    def get_world_bank_data(self, country_code: str, indicator: str, 
                           start_year: str = "2010", end_year: str = "2024") -> Dict:
        """
        Get World Bank economic data
        
        Args:
            country_code: ISO2 code (SG, US, GB) or 'all' for all countries
            indicator: World Bank indicator code
            start_year: Start year (YYYY)
            end_year: End year (YYYY)
        """
        url = f"https://api.worldbank.org/v2/countries/{country_code}/indicators/{indicator}"
        params = {
            'format': 'json',
            'date': f"{start_year}:{end_year}",
            'per_page': 1000
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return {
                'source': 'World Bank',
                'country': country_code,
                'indicator': indicator,
                'data': data[1] if len(data) > 1 else []
            }
        except Exception as e:
            print(f"Error fetching World Bank data for {country_code} ({indicator}): {e}")
            return {'source': 'World Bank', 'error': str(e)}
    
    # ===========================================
    # FRED API (US Federal Reserve)
    # ===========================================
    
    def get_fred_data(self, series_id: str, start_date: str = None, end_date: str = None) -> Dict:
        """Get FRED economic data"""
        if not self.fred_api_key:
            return {'source': 'FRED', 'error': 'FRED API key required'}
        
        url = "https://api.stlouisfed.org/fred/series/observations"
        params = {
            'series_id': series_id,
            'api_key': self.fred_api_key,
            'file_type': 'json'
        }
        if start_date: params['observation_start'] = start_date
        if end_date: params['observation_end'] = end_date
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return {'source': 'FRED', 'series_id': series_id, 'data': response.json().get('observations', [])}
        except Exception as e:
            print(f"Error fetching FRED data: {e}")
            return {'source': 'FRED', 'error': str(e)}
    
    # ===========================================
    # COMBINED METHODS
    # ===========================================
    
    def get_country_overview(self, country_code: str) -> Dict:
        """
        Get comprehensive country data from available free sources
        
        Args:
            country_code: ISO2 country code (e.g., 'SG', 'US', 'GB')
        """
        overview = {
            'country': country_code,
            'timestamp': datetime.now().isoformat(),
            'sources': {}
        }
        
        # World Bank - Key Economic Indicators
        wb_indicators = {
            'GDP': 'NY.GDP.MKTP.CD',           # GDP (current US$)
            'GDP_Growth': 'NY.GDP.MKTP.KD.ZG', # GDP growth (annual %)
            'Population': 'SP.POP.TOTL',        # Total population
            'Unemployment': 'SL.UEM.TOTL.ZS',   # Unemployment (% of total labor force)
            'Inflation': 'FP.CPI.TOTL.ZG',      # Inflation, consumer prices (annual %)
            'Trade_Balance': 'BN.CAB.XOKA.CD'   # Current account balance (BoP, current US$)
        }
        
        wb_data = {}
        for name, indicator in wb_indicators.items():
            data = self.get_world_bank_data(country_code, indicator)
            wb_data[name] = data
            time.sleep(0.1)  # Be respectful to API
        
        overview['sources']['world_bank'] = wb_data
        
        # FRED data (only runs for US and if API key is available)
        if country_code.upper() == 'US' and self.fred_api_key:
            fred_series = {
                'GDP': 'GDP',
                'Unemployment_Rate': 'UNRATE',
                'CPI': 'CPIAUCSL',
                'Federal_Funds_Rate': 'FEDFUNDS'
            }
            fred_data = {}
            for name, series_id in fred_series.items():
                data = self.get_fred_data(series_id)
                fred_data[name] = data
                time.sleep(0.1)
            overview['sources']['fred'] = fred_data
        
        return overview
    
    def get_global_comparison(self, indicator: str, countries: List[str]) -> Dict:
        """Compare specific indicator across multiple countries"""
        comparison = {'indicator': indicator, 'countries': {}, 'timestamp': datetime.now().isoformat()}
        for country in countries:
            data = self.get_world_bank_data(country, indicator)
            comparison['countries'][country] = data
            time.sleep(0.1)
        return comparison
    
    def to_dataframe(self, data: Dict) -> pd.DataFrame:
        """Convert World Bank or FRED API response to pandas DataFrame"""
        try:
            source = data.get('source', '').lower()
            if source == 'world bank' or source == 'fred':
                df = pd.DataFrame(data.get('data', []))
                if not df.empty and 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    # FRED data has '.' for missing values, convert to numeric
                    if 'value' in df.columns:
                        df['value'] = pd.to_numeric(df['value'], errors='coerce')
                    df = df.sort_values('date', ascending=False)
                return df
            return pd.DataFrame(data)
        except Exception as e:
            print(f"Error converting to DataFrame: {e}")
            return pd.DataFrame()

# ===========================================
# USAGE EXAMPLES MODIFIED FOR SINGAPORE
# ===========================================

def main():
    """Example usage of the Free Economic Data API, focusing on Singapore."""
    
    # Load .env file for the FRED_API_KEY
    load_dotenv()
    
    # Initialize (FRED API key is optional but recommended for US data)
    api = FreeEconomicDataAPI(fred_api_key=os.getenv("FRED_API_KEY"))
    
    print("=== Economic Data API Examples (Singapore Focus) ===\n")
    
    # 1. Get a comprehensive overview for Singapore
    print("1. Getting Singapore economic overview...")
    # Use the ISO2 code for Singapore: 'SG'
    sg_overview = api.get_country_overview('SG')
    print("Singapore data sources found:", list(sg_overview['sources'].keys()))
    print("(Note: FRED data is specific to the US and is not included for Singapore)\n")
    
    # 2. Get a specific indicator for Singapore (GDP Growth)
    print("2. Getting Singapore's GDP Growth...")
    sg_gdp_growth = api.get_world_bank_data('SG', 'NY.GDP.MKTP.KD.ZG')
    
    # 3. Convert the specific indicator data to a DataFrame for analysis
    print("\n3. Converting Singapore's GDP Growth data to DataFrame...")
    df = api.to_dataframe(sg_gdp_growth)
    if not df.empty:
        print(f"DataFrame shape: {df.shape}")
        print("Latest GDP Growth data for Singapore (annual %):")
        # Display the most recent, non-null data points
        print(df[['date', 'value']].dropna().head())
    
    # 4. Compare Singapore's inflation to other major economies
    print("\n4. Comparing Inflation (annual %) across countries...")
    inflation_comparison = api.get_global_comparison(
        'FP.CPI.TOTL.ZG',  # Inflation, consumer prices (annual %)
        ['SG', 'US', 'CN', 'JP'] # Singapore, USA, China, Japan
    )
    print(f"Comparison includes data for: {list(inflation_comparison['countries'].keys())}")
    
    # You can then convert each country's data to a dataframe if needed
    sg_inflation_df = api.to_dataframe(inflation_comparison['countries']['SG'])
    print("\nLatest inflation data for Singapore from the comparison:")
    print(sg_inflation_df[['date', 'value']].dropna().head())


if __name__ == "__main__":
    main()

import finnhub

# Initialize the Finnhub client with your API key
finnhub_client = finnhub.Client(api_key="d33a5mpr01qs3viom0e0d33a5mpr01qs3viom0eg")

# Define the company symbol
symbol = 'AAPL'

# Get basic financial metrics
try:
    financials = finnhub_client.company_basic_financials(symbol, 'all')
    print(f"Finnhub Financials for {symbol}:")
    # You can access specific metrics like this:
    # annual_revenue = financials['series']['annual']['revenue'][0]['v']
    # print(f"Latest annual revenue: {annual_revenue}")
    print(financials)

except Exception as e:
    print(f"Error fetching data from Finnhub: {e}")

import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from tavily import TavilyClient
from groq import Groq

# --- Configuration & Blocklists ---
DOMAIN_BLOCKLIST = {
    'wikipedia.org', 'linkedin.com', 'youtube.com', 'facebook.com', 'twitter.com',
    'instagram.com', 'bloomberg.com', 'reuters.com', 'forbes.com', 'cnbc.com',
    'marketwatch.com', 'finance.yahoo.com', 'crunchbase.com'
}
DEFAULT_KEYWORDS = ['investor relations', 'investors', 'financials', 'sec filings', 'berichte', 'finanzberichte']

# --- LLM Functions ---
def get_dynamic_keywords_from_llm(company_name, context_text):
    """Uses Groq to generate context-aware keywords based on scraped text."""
    print("\n-> Asking Groq LLM to generate dynamic keywords from website content...")
    try:
        client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        prompt = f"""
        Based on the following text scraped from the homepage and about pages of a company named '{company_name}', act as a financial web analyst.
        Your task is to generate a concise, comma-separated list of keywords that would be most effective for finding the 'Investor Relations' or 'Financials' page on the company's official website.
        Consider the company's industry, language, and potential corporate structure from the context.

        For example: 'investor relations, investors, SEC filings, about us, corporate information'

        Scraped Text Context:
        ---
        {context_text[:8000]} 
        ---

        Provide only the comma-separated list of keywords.
        """
        response = client.chat.completions.create(
            # UPDATED MODEL NAME
            model="meta-llama/llama-4-maverick-17b-128e-instruct",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
        )
        keywords = [kw.strip() for kw in response.choices[0].message.content.strip().lower().split(',')]
        print(f"   Groq LLM generated keywords: {keywords}")
        return keywords
    except Exception as e:
        print(f"   [Warning] Groq LLM call failed: {e}. Falling back to default keywords.")
        return DEFAULT_KEYWORDS

def generate_financial_report(company_name, text_content):
    """Uses Groq LLM to analyze text and generate a financial report."""
    print("\n-> Generating financial report with Groq LLM...")
    max_chars = 15000
    if len(text_content) > max_chars:
        print(f"   (Trimming content from {len(text_content)} to {max_chars} characters for LLM analysis)")
        text_content = text_content[:max_chars]

    try:
        client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        prompt = f"""
        You are a professional financial analyst. Analyze the following text scraped from the investor relations section of {company_name}'s website and generate a summary of the company's performance for the MOST RECENT financial year you can find.

        Structure your report exactly as follows:
        1.  **Most Recent Financial Year:** Identify and state the year (e.g., "Fiscal Year 2023").
        2.  **Key Financial Highlights:**
            *   **Revenue:** State the total revenue.
            *   **Net Income:** State the net income (or profit).
            *   **Earnings Per Share (EPS):** If available, state the EPS.
        3.  **Management Summary:** Briefly summarize any commentary from management about the results, challenges, or future outlook.
        
        IMPORTANT: If you cannot find a specific figure, clearly state "Information not found in the provided text." Do not invent data.

        Scraped Text:
        ---
        {text_content}
        ---
        """
        response = client.chat.completions.create(
            # UPDATED MODEL NAME
            model="meta-llama/llama-4-maverick-17b-128e-instruct",
            messages=[
                {"role": "system", "content": "You are a professional financial analyst."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1
        )
        return response.choices[0].message.content
    except Exception as e:
        return f"[Error] Could not generate report from Groq LLM: {e}"

# --- Scraping Functions ---
def simple_scrape(url):
    """Scrapes the text content of a single URL."""
    try:
        session = requests.Session()
        session.headers.update({'User-Agent': 'Mozilla/5.0'})
        response = session.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        return ' '.join(soup.get_text().split())
    except requests.RequestException:
        return ""

def find_financials_page(base_url, keywords):
    """Searches a single website for a financials page link."""
    print(f"  -> Checking {base_url} for keywords...")
    try:
        session = requests.Session()
        session.headers.update({'User-Agent': 'Mozilla/5.0'})
        response = session.get(base_url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        for link in soup.find_all('a', href=True):
            link_text = link.get_text().lower().strip()
            if any(keyword in link_text for keyword in keywords):
                return urljoin(base_url, link['href'])
        return None
    except requests.RequestException as e:
        print(f"     [Error] Could not process {base_url}: {e}")
        return None

def recursive_scrape(start_url, depth_limit=1, visited=None):
    """Recursively scrapes text content from a starting URL, staying on the same domain."""
    if visited is None:
        visited = set()
    
    if depth_limit < 0 or start_url in visited:
        return ""

    print(f"  -> Scraping: {start_url} (Depth remaining: {depth_limit})")
    visited.add(start_url)
    base_domain = urlparse(start_url).netloc
    
    scraped_text = simple_scrape(start_url)
    if not scraped_text:
        return ""

    # Re-parse to find links for recursion
    try:
        response = requests.get(start_url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        
        for link in soup.find_all('a', href=True):
            absolute_link = urljoin(start_url, link['href'])
            if urlparse(absolute_link).netloc == base_domain:
                scraped_text += recursive_scrape(absolute_link, depth_limit - 1, visited)
    except requests.RequestException:
        pass # Continue even if a sub-page fails
    
    return scraped_text

# --- Main Workflow Function ---
def main_workflow(company_name):
    """Full workflow: find site, scrape for context, get keywords, find financials page, scrape, and report."""
    print(f"Starting search for '{company_name}'...")
    try:
        tavily_client = TavilyClient(api_key=os.getenv('TAVILY_API_KEY'))
        response = tavily_client.search(f"{company_name} official website", search_depth="basic")
        if not response or 'results' not in response:
            print("Tavily search failed.")
            return
        search_results = response['results']
    except Exception as e:
        print(f"An error occurred during Tavily search: {e}")
        return

    filtered_websites = [
        res['url'] for res in search_results 
        if urlparse(res['url']).netloc.replace('www.', '') not in DOMAIN_BLOCKLIST
    ]
    
    if not filtered_websites:
        print("No potential company websites left after filtering.")
        return

    print("\n-> Scraping initial search results for context...")
    context_text = ""
    for url in filtered_websites[:2]:
        context_text += simple_scrape(url) + " "

    dynamic_keywords = get_dynamic_keywords_from_llm(company_name, context_text)
    
    print(f"\nProceeding to check {len(filtered_websites)} filtered websites with dynamic keywords.")
    for website_url in filtered_websites:
        financials_page_url = find_financials_page(website_url, dynamic_keywords)
        
        if financials_page_url:
            print(f"\n✅ Success! Found potential financials page: {financials_page_url}")
            
            print("\n-> Starting recursive scrape from the financials page...")
            scraped_text = recursive_scrape(financials_page_url)
            
            if scraped_text and len(scraped_text) > 500:
                report = generate_financial_report(company_name, scraped_text)
                print("\n" + "="*25 + " FINANCIAL REPORT " + "="*25)
                print(report)
                print("="*70)
            else:
                print("\n❌ Could not gather enough text content from the website to generate a report.")
            return
            
    print(f"\n❌ Search complete. Could not find a financials page for '{company_name}'.")

# --- Example Usage ---
if __name__ == "__main__":
    main_workflow("NVIDIA")
    print("\n" + "#"*70 + "\n")
    main_workflow("Siemens")

import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from tavily import TavilyClient
from groq import Groq
import json

# --- Configuration & Blocklists ---
DOMAIN_BLOCKLIST = {
    'wikipedia.org', 'linkedin.com', 'youtube.com', 'facebook.com', 'twitter.com',
    'instagram.com', 'bloomberg.com', 'reuters.com', 'forbes.com', 'cnbc.com',
    'marketwatch.com', 'finance.yahoo.com', 'crunchbase.com'
}

# --- LLM Functions ---
def choose_best_financial_link_llm(company_name, links_json):
    """Uses Groq LLM to analyze a list of links and choose the best one."""
    print("\n-> Asking Groq LLM to choose the best link from the homepage...")
    try:
        client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        prompt = f"""
        You are an expert financial analyst. Your task is to identify the single most promising link that leads to a company's financial information, investor relations, or SEC filings.

        I have scraped all the links from the homepage of '{company_name}'. Analyze the list of links below, considering both the link text and the URL structure.

        Return ONLY the full URL of the best choice. Do not add any explanation or other text.

        List of Links (in JSON format):
        ---
        {links_json}
        ---

        The single best URL is:
        """
        response = client.chat.completions.create(
            # UPDATED MODEL NAME
            model="meta-llama/llama-4-maverick-17b-128e-instruct",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.0, # We want the most deterministic choice
        )
        chosen_url = response.choices[0].message.content.strip()
        
        # Basic validation to ensure it's a URL
        if chosen_url.startswith('http'):
            print(f"   LLM chose: {chosen_url}")
            return chosen_url
        else:
            print(f"   [Warning] LLM returned an invalid response: {chosen_url}")
            return None

    except Exception as e:
        print(f"   [Warning] Groq LLM call failed: {e}.")
        return None

def generate_financial_report(company_name, text_content):
    """Uses Groq LLM to analyze text and generate a financial report."""
    print("\n-> Generating financial report with Groq LLM...")
    max_chars = 15000
    if len(text_content) > max_chars:
        text_content = text_content[:max_chars]

    try:
        client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        prompt = f"""
        You are a professional financial analyst. Analyze the following text scraped from the investor relations section of {company_name}'s website and generate a summary for the MOST RECENT financial year you can find.

        Structure your report exactly as follows:
        1.  **Most Recent Financial Year:** (e.g., "Fiscal Year 2023")
        2.  **Key Financial Highlights:**
            *   **Revenue:** 
            *   **Net Income:** 
            *   **Earnings Per Share (EPS):**
        3.  **Management Summary:** 
        
        IMPORTANT: If you cannot find a specific figure, state "Information not found in the provided text." Do not invent data.

        Scraped Text:
        ---
        {text_content}
        ---
        """
        response = client.chat.completions.create(
            # CORRECT MODEL NAME
            model="meta-llama/llama-4-maverick-17b-128e-instruct",
            messages=[
                {"role": "system", "content": "You are a professional financial analyst."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1
        )
        return response.choices[0].message.content
    except Exception as e:
        return f"[Error] Could not generate report from Groq LLM: {e}"

# --- Scraping Functions ---
def gather_all_links(base_url):
    """Scrapes all valid links (text and URL) from a single page."""
    print(f"  -> Gathering all links from: {base_url}")
    try:
        session = requests.Session()
        session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        response = session.get(base_url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        
        links = []
        for link in soup.find_all('a', href=True):
            text = ' '.join(link.get_text().split())
            href = link['href']
            # Filter out irrelevant links
            if text and not href.startswith('#') and not href.startswith('javascript:'):
                full_url = urljoin(base_url, href)
                links.append({'text': text, 'url': full_url})
        return links
    except requests.RequestException as e:
        print(f"     [Error] Failed to scrape {base_url} for links: {e}")
        return []

def simple_scrape(url):
    """Scrapes the clean text content of a single URL."""
    print(f"  -> Scraping page content: {url}")
    try:
        session = requests.Session()
        session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        response = session.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        return ' '.join(soup.get_text().split())
    except requests.RequestException as e:
        print(f"     [Error] Failed to scrape {url}: {e}")
        return ""

# --- Main Workflow Function ---
def main_workflow(company_name):
    """Full workflow: find site, explore its links, let LLM choose the best one, scrape it, and report."""
    print(f"Starting search for '{company_name}'...")
    try:
        tavily_client = TavilyClient(api_key=os.getenv('TAVILY_API_KEY'))
        response = tavily_client.search(f"{company_name} official website", search_depth="basic")
        if not response or 'results' not in response:
            print("Tavily search failed.")
            return
        search_results = response['results']
    except Exception as e:
        print(f"An error occurred during Tavily search: {e}")
        return

    filtered_websites = [
        res['url'] for res in search_results 
        if urlparse(res['url']).netloc.replace('www.', '') not in DOMAIN_BLOCKLIST
    ]
    
    if not filtered_websites:
        print("No potential company websites left after filtering.")
        return

    homepage_url = filtered_websites[0] # Assume the first filtered result is the best homepage
    print(f"\nIdentified potential homepage: {homepage_url}")

    # --- EXPLORE AND DECIDE STAGE ---
    all_links = gather_all_links(homepage_url)
    
    if not all_links:
        print("\n❌ Could not gather any links from the homepage.")
        return

    # Convert links to JSON string for the LLM prompt
    links_json_str = json.dumps(all_links[:50], indent=2) # Limit to first 50 links to avoid huge prompts
    financials_page_url = choose_best_financial_link_llm(company_name, links_json_str)
    
    if financials_page_url:
        print(f"\n✅ Success! LLM identified financials page: {financials_page_url}")
        
        # --- SCRAPE AND REPORT STAGE ---
        scraped_text = simple_scrape(financials_page_url)
        
        if scraped_text and len(scraped_text) > 200:
            report = generate_financial_report(company_name, scraped_text)
            print("\n" + "="*25 + " FINANCIAL REPORT " + "="*25)
            print(report)
            print("="*70)
        else:
            print("\n❌ Could not gather enough text from the chosen page to generate a report.")
    else:
        print(f"\n❌ Search complete. LLM could not identify a financials page for '{company_name}'.")

# --- Example Usage ---
if __name__ == "__main__":
    main_workflow("TE Connectivity")
    print("\n" + "#"*70 + "\n")
    main_workflow("Broadcom")

# %%
import os
import json
from typing import Any, Dict, List, Optional, TypedDict, Literal
from dotenv import load_dotenv

# pydantic for data validation
from pydantic import BaseModel, Field

# LangChain and LangGraph components
from langgraph.graph import StateGraph, START, END
from langchain_groq import ChatGroq
from langchain_core.messages import SystemMessage, HumanMessage, ToolMessage, AIMessage
from langchain.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser
from langchain_core.tools import tool

# Web search
from tavily import TavilyClient

# Financial data
import yfinance as yf

# Web Scraping and HTTP requests for new competitor analysis
import requests
from bs4 import BeautifulSoup

# Visualization (for generating plot JSON)
import plotly.graph_objects as go
from curl_cffi import requests as cffi_requests # Using cffi for yfinance session
import logging

# %%
# --- CONFIGURATION ---

# Load environment variables from .env file
load_dotenv()

# Initialize the language model and Tavily client
llm = ChatGroq(model_name='meta-llama/llama-4-maverick-17b-128e-instruct', api_key=os.getenv('GROQ_API_KEY'))
tavily_client = TavilyClient(api_key=os.getenv('TAVILY_API_KEY'))


# Constants to control the depth of the research loops
MAX_PRIMARY_RESEARCH_LOOPS = 3

# Use a session for yfinance to improve performance
session = cffi_requests.Session(impersonate="chrome")


# %%
# --- AGENT STATE DEFINITION ---

class State(TypedDict):
    """
    Defines the shared state that flows through the agent's graph.
    """
    # Core routing and conversation management
    route: Literal['Web_query', 'Normal_query', 'Financial_Analysis', 'Plot_Graph', 'Market_Report']
    research_topic: str
    messages: List[Any]

    # State for the intensive research loops
    search_query: str
    web_research_results: str
    running_summary: str
    research_loop_count: int

    # State for plotting financial charts
    plot_type: Optional[str]
    ticker: Optional[str]
    plot_json: Optional[str]

    # State for the market report context
    company: str
    industry: str
    competitors: List[str]
    competitor_data: Dict[str, Any]
    # A concise summary for storing in conversational context
    context_summary: Optional[str]


# %%
# --- HELPER FUNCTIONS & TOOLS ---

def fetch_stock_data(ticker, period="1y"):
    """Fetches historical stock data for a given ticker."""
    stock = yf.Ticker(ticker, session=session)
    return stock.history(period=period)

def plot_candles_stick(df, title=""):
    """Generates a candlestick chart from a DataFrame."""
    fig = go.Figure(data=[go.Candlestick(x=df.index, open=df['Open'], high=df['High'], low=df['Low'], close=df['Close'])])
    fig.update_layout(title=title)
    return fig

def tavily_search(query: str, max_results: int = 4) -> str:
    """Performs a web search using Tavily and returns formatted results."""
    try:
        results = tavily_client.search(query, max_results=max_results)['results']
        return "\n\n".join([f"Source: {res['title']}\nURL: {res['url']}\nContent: {res['content']}" for res in results])
    except Exception as e:
        return f"Error during web search: {e}"

@tool
def last_close_price(ticker: str) -> float:
    """Returns the last closing price for a given stock ticker."""
    ticker_obj = yf.Ticker(ticker, session=session)
    info = ticker_obj.info
    return info.get('previousClose', 'N/A')

@tool
def total_debt(ticker: str) -> float:
    """Returns the total debt for a given stock ticker."""
    ticker_obj = yf.Ticker(ticker, session=session)
    info = ticker_obj.info
    return info.get('totalDebt', 'N/A')

finance_tools = [last_close_price, total_debt]
finance_tool_map = {t.name: t for t in finance_tools}


# %%
# --- AGENT ROUTING LOGIC ---

class Route_First_Step(BaseModel):
    """The initial routing model to decide the agent's path."""
    step: Literal['Market_Report', 'Web_query', 'Normal_query', 'Financial_Analysis', 'Plot_Graph'] = Field(None)
    company: Optional[str] = Field(None)
    industry: Optional[str] = Field(None)


# %%
# --- IN-DEPTH FINANCIAL ANALYSIS FUNCTION ---

def perform_financial_analysis(company_name: str) -> str:
    """
    Performs a detailed financial analysis of a company.
    """
    print(f"\n--- Performing In-Depth Financial Analysis for: {company_name} ---")
    try:
        search_results = tavily_client.search(query=f"stock ticker for {company_name}", max_results=1)['results']
        if not search_results:
            return "Could not find a stock ticker for this company, it may be private."
        # Extract ticker more robustly
        ticker_symbol = search_results[0]['content'].split('Ticker symbol: ')[-1].split(',')[0].strip()
        print(f" found ticker: {ticker_symbol}")
    except Exception as e:
        return f"Error finding ticker for {company_name}: {e}"

    try:
        ticker = yf.Ticker(ticker_symbol, session=session)
        info = ticker.info
        key_metrics = {
            "Market Cap": info.get("marketCap"), "Enterprise Value": info.get("enterpriseValue"),
            "Trailing P/E": info.get("trailingPE"), "Forward P/E": info.get("forwardPE"),
            "PEG Ratio": info.get("pegRatio"), "Price to Sales (ttm)": info.get("priceToSalesTrailing12Months"),
            "Price to Book": info.get("priceToBook"), "Return on Equity": info.get("returnOnEquity"),
            "Total Revenue": info.get("totalRevenue"), "Gross Profit": info.get("grossProfits"),
            "EBITDA": info.get("ebitda"), "Net Income": info.get("netIncomeToCommon"),
            "Total Debt": info.get("totalDebt"), "Total Cash": info.get("totalCash"),
            "Operating Cash Flow": info.get("operatingCashflow"), "Levered Free Cash Flow": info.get("freeCashflow"),
        }
        financial_data = {k: f"{v:,}" for k, v in key_metrics.items() if v is not None}
        if not financial_data:
            return "No financial data could be retrieved from yfinance."
        formatted_data_string = "\n".join([f"- {key}: {value}" for key, value in financial_data.items()])
        print(" successfully fetched financial data.")
    except Exception as e:
        return f"Error fetching financial data from yfinance for {ticker_symbol}: {e}"
        
    print(" generating financial summary with LLM...")
    summary_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a senior financial analyst. Provide a concise summary of a company's financial health based ONLY on the key metrics provided."),
        ("human", "Analyze the following data for {company_name} and generate a brief summary focusing on profitability, valuation, and debt. Data:\n{financial_data}")
    ])
    summary_chain = summary_prompt | llm | StrOutputParser()
    return summary_chain.invoke({"company_name": company_name, "financial_data": formatted_data_string})

# %%
# --- AGENT NODES (CORE FUNCTIONS) ---

def web_research_node(state: State) -> dict:
    """Node for performing a web search."""
    print(f"--- Performing Web Research on: {state['search_query']} ---")
    results = tavily_search(state['search_query'])
    return {"web_research_results": results, "research_loop_count": state.get('research_loop_count', 0) + 1}

def answer_normal_query(state: State) -> dict:
    """Node for answering a general knowledge question."""
    chain = ChatPromptTemplate.from_messages([("system", "You are a helpful assistant."), ("human", "{query}")]) | llm | StrOutputParser()
    return {"running_summary": chain.invoke({"query": state['research_topic']})}

def call_financial_llm(state: State) -> dict:
    messages = [SystemMessage(content="You are a financial analyst...")] + state['messages']
    llm_with_tools = llm.bind_tools(finance_tools, tool_choice='auto')
    message = llm_with_tools.invoke(messages)
    return {'messages': state['messages'] + [message]}

def exists_action(state: State) -> bool:
    return len(state['messages'][-1].tool_calls) > 0

def take_action(state: State) -> dict:
    tool_calls = state['messages'][-1].tool_calls
    tool_results = [ToolMessage(tool_call_id=t['id'], name=t['name'], content=str(finance_tool_map[t['name']].invoke(t['args']))) for t in tool_calls]
    summary = "\n".join([f"Data for {res.name}: {res.content}" for res in tool_results])
    return {'messages': state['messages'] + tool_results, 'running_summary': summary}

def parse_plot_query(state: State) -> dict:
    words = state["research_topic"].lower().split()
    ticker = words[-1].upper()
    plot_type = "candlestick" if "candlestick" in state["research_topic"].lower() else None
    return {"plot_type": plot_type, "ticker": ticker}

def generate_plot(state: State) -> dict:
    try:
        if state.get("plot_type") == "candlestick":
            df = fetch_stock_data(state["ticker"])
            fig = plot_candles_stick(df, title=f"{state['ticker']} Candlestick Chart")
            return {"plot_json": fig.to_json(), "running_summary": f"Generated candlestick plot for {state['ticker']}."}
        else:
            return {"running_summary": "Plot type not supported or not specified."}
    except Exception as e:
        return {"running_summary": f"Error generating plot: {e}"}

def start_report_generation(state: State) -> dict:
    print("--- Starting Intensive Market Report Generation (Phase 1: Primary Company) ---")
    return {"search_query": f"Initial overview of {state['company']} in the {state['industry']} industry, including key players, market size, and SWOT analysis.", "running_summary": "", "research_loop_count": 0}

def summarize_and_update(state: State) -> dict:
    print("--- Summarizing and Updating Report ---")
    chain = ChatPromptTemplate.from_messages([("system", "You are a market research analyst. Integrate new findings into the existing summary."), ("human", "<Existing_Summary>\n{existing_summary}\n</Existing_Summary>\n\n<New_Information>\n{new_information}\n</New_Information>\n\nProvide the updated, integrated summary.")]) | llm | StrOutputParser()
    return {"running_summary": chain.invoke({"existing_summary": state['running_summary'], "new_information": state['web_research_results']})}

def reflect_on_summary(state: State) -> dict:
    print("--- Reflecting on Research and Identifying Gaps ---")
    chain = ChatPromptTemplate.from_messages([("system", "You are a senior market analyst. Find the single most critical knowledge gap and formulate a targeted web search query to fill it."), ("human", "<Research_Summary>\n{summary}\n</Research_Summary>\n\nGenerate the next single web search query.")]) | llm | StrOutputParser()
    return {"search_query": chain.invoke({"summary": state['running_summary']})}

def decide_if_continue_primary_research(state: State) -> str:
    print(f"--- Primary Research Loop, Count: {state['research_loop_count']} ---")
    return "analyze_primary_company_finance" if state['research_loop_count'] >= MAX_PRIMARY_RESEARCH_LOOPS else "reflect"

def analyze_primary_company_finance_node(state: State) -> dict:
    financial_summary = perform_financial_analysis(state['company'])
    return {"running_summary": state['running_summary'] + "\n\n## In-Depth Financial Analysis\n" + financial_summary}
    
def get_competitors_node(state: State) -> dict:
    """Finds and returns a list of up to 3 competitors for a given company and industry."""
    company, industry = state['company'], state['industry']
    print(f"\n--- Identifying top 3 competitors for {company} in the {industry} industry... ---")
    try:
        search_context = "\n\n".join([r['content'] for r in tavily_client.search(query=f"key direct competitors of {company} in the {industry} market", max_results=5)['results']])
        chain = ChatPromptTemplate.from_messages([("system", "You are an expert market analyst. Return a comma-separated list of main competitors based on the text."), ("human", "Text about {company}: <text>{search_context}</text>")]) | llm | StrOutputParser()
        competitor_string = chain.invoke({"company": company, "search_context": search_context})
        
        # MODIFIED: Limit the list to the first 3 competitors
        competitors = [name.strip() for name in competitor_string.split(',') if name.strip() and name.strip().lower() != company.lower()]
        top_competitors = competitors[:3]
        
        print(f"Found competitors: {top_competitors}")
        return {"competitors": top_competitors}
    except Exception as e:
        print(f"Error during competitor search: {e}")
        return {"competitors": []}

def competitor_deep_dive_node(state: State) -> Dict[str, Any]:
    """Gathers detailed intelligence on each of the top competitors."""
    all_competitor_data = {}
    for name in state['competitors']:
        print(f"\n--- Researching Competitor: {name} ---")
        try:
            website_url = tavily_client.search(query=f"official website of {name}", max_results=1)['results'][0]['url']
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
            soup = BeautifulSoup(requests.get(website_url, headers=headers, timeout=10).text, 'html.parser')
            raw_text = soup.body.get_text(separator=' ', strip=True)[:4000]
            website_summary = (ChatPromptTemplate.from_template("Summarize this website content: <text>{text}</text>") | llm | StrOutputParser()).invoke({"text": raw_text})
        except Exception as e:
            website_summary = f"Could not process website: {e}"
        try:
            news_context = "\n\n".join([r['content'] for r in tavily_client.search(f"latest news for {name}", max_results=3)['results']])
            recent_news = (ChatPromptTemplate.from_template("Summarize these news articles about {name}:\n<articles>{articles}</articles>") | llm | StrOutputParser()).invoke({"name": name, "articles": news_context})
        except Exception as e:
            recent_news = f"Error gathering news: {e}"
        
        financial_summary = perform_financial_analysis(name)
        all_competitor_data[name] = {"website_summary": website_summary, "recent_news": recent_news, "financial_summary": financial_summary}
    return {"competitor_data": all_competitor_data}

def compile_final_report_node(state: State) -> dict:
    """Generates the final report based on all gathered data."""
    print("\n--- Generating Final Competitor Analysis Report ---")
    formatted_data = f"Primary Company Research Summary:\n{state['running_summary']}\n\n--- Competitor Intelligence Data ---\n\n"
    for name, data in state['competitor_data'].items():
        formatted_data += f"Competitor: {name}\nWebsite Summary: {data['website_summary']}\nRecent News Summary: {data['recent_news']}\nFinancial Health Summary: {data['financial_summary']}\n\n"

    report_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a senior market analyst."),
        ("human", "Generate a comprehensive market analysis report using the following data.\n<data>{formatted_data}</data>\n\nStructure your report with: 1. Executive Summary, 2. Primary Company Analysis, 3. Primary Company Financial Health, 4. Competitive Landscape Deep-Dive (with sections for each competitor's Business, News, and Financials), 5. Comparative Analysis & Strategic Outlook. Use markdown.")
    ])
    final_report = (report_prompt | llm | StrOutputParser()).invoke({"formatted_data": formatted_data})
    return {"running_summary": final_report}

def summarize_for_context_node(state: State) -> dict:
    """Summarizes the final report for concise conversational context."""
    print("--- Summarizing Final Report for Conversational Context ---")
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are an AI assistant. Create a very brief summary (2-3 sentences) of the following report for use as conversational memory. Mention the primary company and its key competitors."),
        ("human", "Please summarize this report:\n\n<report>{report_text}</report>")
    ])
    context_summary = (prompt | llm | StrOutputParser()).invoke({"report_text": state.get('running_summary', '')})
    return {"context_summary": context_summary}

# %%
# --- GRAPH DEFINITION & WIRING ---

def build_agent():
    """Builds and compiles the agent's state graph."""
    workflow = StateGraph(State)
    nodes = [
        ("route_first_step", call_route_first_step), ("answer_normal_query", answer_normal_query),
        ('call_llm', call_financial_llm), ('take_action', take_action),
        ("parse_plot_query", parse_plot_query), ("generate_plot", generate_plot),
        ("start_report", start_report_generation), ("web_research", web_research_node),
        ("summarize", summarize_and_update), ("reflect", reflect_on_summary),
        ("analyze_primary_company_finance", analyze_primary_company_finance_node),
        ("get_competitors", get_competitors_node), ("competitor_deep_dive", competitor_deep_dive_node),
        ("compile_report", compile_final_report_node), ("summarize_for_context", summarize_for_context_node)
    ]
    for name, node in nodes:
        workflow.add_node(name, node)

    workflow.add_edge(START, "route_first_step")
    workflow.add_conditional_edges("route_first_step", lambda x: x.get('route', ''), {'Market_Report': 'start_report', 'Normal_query': 'answer_normal_query', 'Financial_Analysis': 'call_llm', 'Plot_Graph': 'parse_plot_query'})
    workflow.add_edge("start_report", "web_research")
    workflow.add_edge("web_research", "summarize")
    workflow.add_edge("reflect", "web_research") 
    workflow.add_conditional_edges("summarize", decide_if_continue_primary_research, {"reflect": "reflect", "analyze_primary_company_finance": "analyze_primary_company_finance"})
    workflow.add_edge("analyze_primary_company_finance", "get_competitors")
    workflow.add_edge("get_competitors", "competitor_deep_dive")
    workflow.add_edge("competitor_deep_dive", "compile_report")
    workflow.add_edge("compile_report", "summarize_for_context") 
    
    workflow.add_edge("answer_normal_query", END)
    workflow.add_edge("summarize_for_context", END) 
    workflow.add_edge("parse_plot_query", "generate_plot")
    workflow.add_edge("generate_plot", END)
    workflow.add_conditional_edges("call_llm", exists_action, {True: "take_action", False: END})
    workflow.add_edge("take_action", END)
    
    return workflow.compile()

# %%
# --- CHATBOT CLASS & MAIN EXECUTION ---
def call_route_first_step(state: State):
    """The main router node that directs the agent's path."""
    print("---Routing Initial Query---")
    routing = (llm.with_structured_output(Route_First_Step)).invoke(state["research_topic"])
    print(f"Routing result: {routing.step}")
    return {"route": routing.step, "company": routing.company, "industry": routing.industry} if routing.step == 'Market_Report' else {"route": routing.step}

class FinancialChatBot:
    """A class to manage the conversation and state."""
    def __init__(self):
        self.model = build_agent()
        self.conversation_history = []
        self.context_messages = []

    def _update_context(self, user_input: str, bot_response: str):
        """Helper to manage the conversation history for the model."""
        self.context_messages.extend([HumanMessage(content=user_input), AIMessage(content=bot_response)])
        if len(self.context_messages) > 6:
            self.context_messages = self.context_messages[-6:]

    def chat(self, user_input: str, company: str = None, industry: str = None) -> dict:
        self.conversation_history.append(f"👤 User: {user_input}")
        initial_state = {"research_topic": user_input, "messages": self.context_messages + [HumanMessage(content=user_input)], "company": company, "industry": industry}
        try:
            final_state = self.model.invoke(initial_state)
            text_response = final_state.get('running_summary', 'I could not find an answer.')
            context_for_history = final_state.get('context_summary') or text_response
            self._update_context(user_input, context_for_history)
            self.conversation_history.append(f"🤖 Assistant: {text_response}")
            return {"text": text_response, "plot": final_state.get('plot_json')}
        except Exception as e:
            import traceback
            error_message = f"An error occurred: {e}\n{traceback.format_exc()}"
            self.conversation_history.append(f"🤖 Assistant: {error_message}")
            return {"text": error_message, "plot": None}

def main():
    """The main function to run the chatbot in the console."""
    chatbot = FinancialChatBot()
    print("Welcome to the Advanced Financial Analyst Assistant!")
    print("Type 'quit' to exit.")
    mode = input("Type 'report' to generate a market report, or press Enter to start a chat: ").strip().lower()

    if mode == 'report':
        company = input("Please enter the company name: ").strip()
        industry = input("Please enter the industry: ").strip()
        print("\nGenerating your intensive market report on the top 3 competitors. This may take a few minutes...")
        query = f"Generate an intensive market report for {company} in the {industry} industry."
        response = chatbot.chat(query, company=company, industry=industry)
        print("\n\n" + "="*60 + "\n            Intensive Market Research Report\n" + "="*60 + "\n")
        print(response['text'])

    while True:
        user_input = input("\n👤 You: ").strip()
        if user_input.lower() == 'quit':
            print("\nGoodbye!")
            break
        response = chatbot.chat(user_input)
        print(f"\n🤖 Assistant:\n{response['text']}")
        if response['plot']:
            print("\n[A plot has been generated and would be displayed in a UI.]")

if __name__ == "__main__":
    main()

import os
import requests
import pandas as pd
from datetime import datetime, timedelta
import json
import time
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

# Load .env file for API keys
load_dotenv()

# ===========================================
# FreeEconomicDataAPI Class (Modified - No FRED)
# ===========================================

class FreeEconomicDataAPI:
    """
    Comprehensive API client for free economic data sources,
    focusing on World Bank and other potential free sources.
    """
    def __init__(self): # Removed fred_api_key parameter
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Economic-Data-Client/1.0'
        })

    # ===========================================
    # WORLD BANK API (Primary Source)
    # ===========================================

    def get_world_bank_countries(self) -> List[Dict]:
        """Get list of all countries from World Bank"""
        url = "https://api.worldbank.org/v2/countries"
        params = {'format': 'json', 'per_page': 500}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return data[1] if len(data) > 1 else []
        except Exception as e:
            print(f"Error fetching World Bank countries: {e}")
            return []

    def get_world_bank_indicators(self) -> List[Dict]:
        """Get list of all available indicators from World Bank"""
        url = "https://api.worldbank.org/v2/indicators"
        params = {'format': 'json', 'per_page': 1000}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return data[1] if len(data) > 1 else []
        except Exception as e:
            print(f"Error fetching World Bank indicators: {e}")
            return []

    def get_world_bank_data(self, country_code: str, indicator: str, 
                           start_year: str = "2010", end_year: str = "2024") -> Dict:
        """
        Get World Bank economic data
        
        Args:
            country_code: ISO2 code (SG, US, GB) or 'all' for all countries
            indicator: World Bank indicator code
            start_year: Start year (YYYY)
            end_year: End year (YYYY)
        """
        url = f"https://api.worldbank.org/v2/countries/{country_code}/indicators/{indicator}"
        params = {
            'format': 'json',
            'date': f"{start_year}:{end_year}",
            'per_page': 1000
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return {
                'source': 'World Bank',
                'country': country_code,
                'indicator': indicator,
                'data': data[1] if len(data) > 1 else []
            }
        except Exception as e:
            print(f"Error fetching World Bank data for {country_code} ({indicator}): {e}")
            return {'source': 'World Bank', 'error': str(e)}

    # Removed FRED API methods

    # ===========================================
    # COMBINED METHODS (Modified - No FRED)
    # ===========================================

    def get_country_overview(self, country_code: str) -> Dict:
        """
        Get comprehensive country data from available free sources (World Bank).
        
        Args:
            country_code: ISO2 country code (e.g., 'SG', 'US', 'GB')
        """
        overview = {
            'country': country_code,
            'timestamp': datetime.now().isoformat(),
            'sources': {}
        }
        
        # World Bank - Key Economic Indicators
        wb_indicators = {
            'GDP': 'NY.GDP.MKTP.CD',           # GDP (current US$)
            'GDP_Growth': 'NY.GDP.MKTP.KD.ZG', # GDP growth (annual %)
            'Population': 'SP.POP.TOTL',        # Total population
            'Unemployment': 'SL.UEM.TOTL.ZS',   # Unemployment (% of total labor force)
            'Inflation': 'FP.CPI.TOTL.ZG',      # Inflation, consumer prices (annual %)
            'Trade_Balance': 'BN.CAB.XOKA.CD'   # Current account balance (BoP, current US$)
        }
        
        wb_data = {}
        for name, indicator in wb_indicators.items():
            data = self.get_world_bank_data(country_code, indicator)
            wb_data[name] = data
            time.sleep(0.1)  # Be respectful to API
        
        overview['sources']['world_bank'] = wb_data
        
        # Removed FRED data section
        
        return overview

    def get_global_comparison(self, indicator: str, countries: List[str]) -> Dict:
        """Compare specific indicator across multiple countries"""
        comparison = {'indicator': indicator, 'countries': {}, 'timestamp': datetime.now().isoformat()}
        for country in countries:
            data = self.get_world_bank_data(country, indicator)
            comparison['countries'][country] = data
            time.sleep(0.1)
        return comparison

    def to_dataframe(self, data: Dict) -> pd.DataFrame:
        """Convert World Bank API response to pandas DataFrame"""
        try:
            source = data.get('source', '').lower()
            if source == 'world bank': # Only World Bank now
                df = pd.DataFrame(data.get('data', []))
                if not df.empty and 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    # World Bank data doesn't use '.', but ensure numeric conversion
                    if 'value' in df.columns:
                        df['value'] = pd.to_numeric(df['value'], errors='coerce')
                    df = df.sort_values('date', ascending=False)
                return df
            return pd.DataFrame(data)
        except Exception as e:
            print(f"Error converting to DataFrame: {e}")
            return pd.DataFrame()


# --- Helper functions for data formatting for LLM (Modified - No FRED) ---

def format_world_bank_indicator_data_for_llm(data_response: Dict) -> str:
    """
    Formats the data portion of a single World Bank indicator response
    into a human-readable string for an LLM, showing the latest 5 entries.
    `data_response` is the full dictionary from get_world_bank_data.
    """
    if not data_response or 'data' not in data_response or not data_response['data']:
        return f"No data available for indicator {data_response.get('indicator', 'Unknown')} in {data_response.get('country', 'Unknown')}"
    
    df = pd.DataFrame(data_response['data'])
    if df.empty or 'date' not in df.columns or 'value' not in df.columns:
        return f"No valid data points for indicator {data_response.get('indicator', 'Unknown')} in {data_response.get('country', 'Unknown')}"
        
    df['date'] = pd.to_datetime(df['date'], errors='coerce')
    df['value'] = pd.to_numeric(df['value'], errors='coerce')
    df = df.dropna(subset=['date', 'value']).sort_values('date', ascending=False)
    
    if df.empty:
        return f"No valid data points after cleaning for indicator {data_response.get('indicator', 'Unknown')} in {data_response.get('country', 'Unknown')}"
        
    indicator_id = data_response.get('indicator', 'Unknown Indicator Code')
    country_code = data_response.get('country', 'Unknown Country')
    
    # Try to get indicator name from the first data point if available, otherwise use code
    indicator_name = df.iloc[0]['indicator']['value'] if not df.empty and 'indicator' in df.columns and isinstance(df.iloc[0]['indicator'], dict) else indicator_id
    
    formatted_entries = []
    for _, row in df.head(5).iterrows(): # Latest 5 data points
        year = row['date'].year
        value = row['value']
        formatted_entries.append(f"{year}: {value:,.2f}")
    
    return f"'{indicator_name}' for {country_code}: {'; '.join(formatted_entries)}"

# Removed format_fred_series_data_for_llm

def prepare_country_overview_for_llm(overview_data: Dict) -> str:
    """Converts the structured country overview into a single string for LLM."""
    country = overview_data.get('country', 'Unknown Country')
    timestamp = overview_data.get('timestamp', 'N/A')
    
    llm_input = f"Economic Overview for {country} (as of {timestamp})\n\n"
    
    if 'world_bank' in overview_data['sources']:
        llm_input += "--- World Bank Data ---\n"
        for indicator_name, wb_indicator_response in overview_data['sources']['world_bank'].items():
            llm_input += f"  - {format_world_bank_indicator_data_for_llm(wb_indicator_response)}\n"
        llm_input += "\n"
        
    # Removed FRED data section
        
    return llm_input

def prepare_global_comparison_for_llm(comparison_data: Dict) -> str:
    """Converts the structured global comparison into a single string for LLM."""
    indicator_id = comparison_data.get('indicator', 'Unknown Indicator Code')
    timestamp = comparison_data.get('timestamp', 'N/A')
    
    llm_input = f"Global Comparison for Indicator '{indicator_id}' (as of {timestamp})\n\n"
    
    for country, wb_indicator_response in comparison_data.get('countries', {}).items():
        llm_input += f"  - {format_world_bank_indicator_data_for_llm(wb_indicator_response)}\n"
        
    return llm_input

# ===========================================
# LangGraph Integration (Modified - No FRED)
# ===========================================

from langgraph.graph import StateGraph, END
from typing import TypedDict
from groq import Groq
from langchain_core.messages import HumanMessage, SystemMessage

# Define the state for our LangGraph agent
class AgentState(TypedDict):
    """
    Represents the state of our economic analysis agent.
    """
    api_client: FreeEconomicDataAPI
    request_type: str # e.g., 'country_overview', 'global_comparison'
    country_code: Optional[str]
    indicator_code: Optional[str]
    comparison_countries: Optional[List[str]]
    raw_data: Optional[Dict[str, Any]]
    llm_input_text: Optional[str]
    llm_insights: Optional[str]
    error_message: Optional[str]

# --- LangGraph Nodes ---

def fetch_economic_data(state: AgentState) -> AgentState:
    """Fetches data using the FreeEconomicDataAPI based on the request_type."""
    api = state['api_client']
    request_type = state['request_type']
    
    try:
        if request_type == 'country_overview':
            country_code = state['country_code']
            raw_data = api.get_country_overview(country_code)
        elif request_type == 'global_comparison':
            indicator_code = state['indicator_code']
            comparison_countries = state['comparison_countries']
            raw_data = api.get_global_comparison(indicator_code, comparison_countries)
        else:
            raise ValueError(f"Unknown request type: {request_type}")
        
        state['raw_data'] = raw_data
        state['error_message'] = None
    except Exception as e:
        state['error_message'] = f"Error fetching data: {e}"
        state['raw_data'] = None
        print(f"Error in fetch_economic_data: {e}")
        
    return state

def format_data_for_llm(state: AgentState) -> AgentState:
    """Formats the fetched raw data into a text string suitable for an LLM."""
    raw_data = state['raw_data']
    request_type = state['request_type']

    if state['error_message']: # If previous node had an error, propagate it
        state['llm_input_text'] = f"An error occurred during data fetching: {state['error_message']}"
        return state

    try:
        if request_type == 'country_overview':
            llm_input = prepare_country_overview_for_llm(raw_data)
        elif request_type == 'global_comparison':
            llm_input = prepare_global_comparison_for_llm(raw_data)
        else:
            llm_input = "Could not format data: Invalid request type."
            
        state['llm_input_text'] = llm_input
        state['error_message'] = None
    except Exception as e:
        state['error_message'] = f"Error formatting data for LLM: {e}"
        state['llm_input_text'] = "Error: Data formatting failed."
        print(f"Error in format_data_for_llm: {e}")
        
    return state

def generate_insights_with_llm(state: AgentState) -> AgentState:
    """Sends the formatted data to Groq and gets economic insights."""
    groq_api_key = os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        state['error_message'] = "GROQ_API_KEY not found in environment variables."
        state['llm_insights'] = "Cannot generate insights without Groq API key."
        return state

    if state['error_message']: # Skip LLM if previous step had an error
        state['llm_insights'] = f"Cannot generate insights due to prior error: {state['error_message']}"
        return state

    client = Groq(api_key=groq_api_key)
    llm_input_text = state['llm_input_text']
    country_code = state.get('country_code', 'the specified country')
    
    # --- Prompt Engineering: Crafting an effective prompt ---
    system_message_content = (
        "You are an expert economic analyst. Your task is to analyze the provided economic data "
        "and provide clear, concise, and insightful summaries. Identify key trends, significant changes, "
        "and relevant comparisons. Structure your output logically with bullet points or paragraphs, "
        "highlighting the most important findings. Focus on the most recent data points and historical context over the last 5-10 years."
    )
    
    user_message_content = (
        f"Analyze the following economic data. Focus on key indicators, trends over the last 5 years, "
        f"and make comparisons between countries if multiple are provided. "
        f"Specifically, for {country_code if state['request_type'] == 'country_overview' else 'the countries in the comparison'}, what are the most significant economic insights and trends? "
        f"Highlight any potential implications or observations.\n\n"
        f"Economic Data:\n{llm_input_text}\n\n"
        "Provide your analysis with clear headings and bullet points for readability. If there are any limitations in the data, mention them."
    )
    
    messages = [
        SystemMessage(content=system_message_content),
        HumanMessage(content=user_message_content)
    ]
    
    try:
        chat_completion = client.chat.completions.create(
            messages=messages,
            model="llama3-8b-8192", # Or "llama3-70b-8192" for more powerful analysis
            temperature=0.7,
            max_tokens=1024 # Limit token generation to avoid excessive cost/length
        )
        
        state['llm_insights'] = chat_completion.choices[0].message.content
        state['error_message'] = None
    except Exception as e:
        state['error_message'] = f"Error calling Groq API: {e}"
        state['llm_insights'] = "Failed to generate insights due to an API error. Check Groq API key and network connection."
        print(f"Error in generate_insights_with_llm: {e}")
        
    return state

# --- Build the LangGraph Workflow ---

# --- Build the LangGraph Workflow ---

workflow = StateGraph(AgentState)

# Add nodes to the graph
workflow.add_node("fetch_data", fetch_economic_data)
workflow.add_node("format_data", format_data_for_llm)
workflow.add_node("generate_insights", generate_insights_with_llm)

# Define the entry point
workflow.set_entry_point("fetch_data") # <--- ADD THIS LINE

# Define the sequence of steps
workflow.add_edge("fetch_data", "format_data")
workflow.add_edge("format_data", "generate_insights")
workflow.add_edge("generate_insights", END) # End the graph after generating insights

# Compile the graph
app = workflow.compile()

# --- Example Usage with LangGraph ---

if __name__ == "__main__":
    api_client_instance = FreeEconomicDataAPI() # No FRED API key needed in init

    print("--- Running LangGraph for Singapore Economic Overview ---")
    inputs_sg_overview = {
        "api_client": api_client_instance,
        "request_type": "country_overview",
        "country_code": "SG"
    }
    
    final_state_sg = None
    print("\nStarting stream for Singapore Overview...")
    for s in app.stream(inputs_sg_overview):
        print(f"Current state: {list(s.keys())}") # Print keys to avoid verbose output
        final_state_sg = s

    if final_state_sg and 'llm_insights' in final_state_sg:
        print("\n--- LLM Insights for Singapore Economic Overview ---")
        print(final_state_sg['llm_insights'])
    elif final_state_sg and 'error_message' in final_state_sg:
        print(f"\n--- Error --- \n{final_state_sg['error_message']}")

    print("\n" + "="*80 + "\n")

    print("--- Running LangGraph for Global Inflation Comparison ---")
    inputs_inflation_comparison = {
        "api_client": api_client_instance,
        "request_type": "global_comparison",
        "indicator_code": "FP.CPI.TOTL.ZG", # Inflation, consumer prices (annual %)
        "comparison_countries": ['SG', 'US', 'CN', 'JP']
    }

    final_state_comparison = None
    print("\nStarting stream for Global Inflation Comparison...")
    for s in app.stream(inputs_inflation_comparison):
        print(f"Current state: {list(s.keys())}") # Print keys to avoid verbose output
        final_state_comparison = s

    if final_state_comparison and 'llm_insights' in final_state_comparison:
        print("\n--- LLM Insights for Global Inflation Comparison ---")
        print(final_state_comparison['llm_insights'])
    elif final_state_comparison and 'error_message' in final_state_comparison:
        print(f"\n--- Error --- \n{final_state_comparison['error_message']}")

import os
import requests
import pandas as pd
from datetime import datetime, timedelta
import json
import time
from typing import Dict, List, Optional, Any, TypedDict, Annotated
from dotenv import load_dotenv
import operator

# Langchain and Groq imports
from groq import Groq
from langchain_groq import ChatGroq
from langgraph.graph import StateGraph, END

# --- Start of your FreeEconomicDataAPI Class (Copied verbatim) ---
class FreeEconomicDataAPI:
    """
    Comprehensive API client for free economic data sources
    Combines World Bank, IMF, FRED, OECD, and other free sources
    """
    
    def __init__(self, fred_api_key: Optional[str] = None):
        """
        Initialize the API client
        
        Args:
            fred_api_key: Optional FRED API key (free from https://fred.stlouisfed.org/docs/api/)
        """
        self.fred_api_key = fred_api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Economic-Data-Client/1.0'
        })
    
    # ===========================================
    # WORLD BANK API (Primary Source)
    # ===========================================
    
    def get_world_bank_countries(self) -> List[Dict]:
        """Get list of all countries from World Bank"""
        url = "https://api.worldbank.org/v2/countries"
        params = {'format': 'json', 'per_page': 500}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return data[1] if len(data) > 1 else []
        except Exception as e:
            print(f"Error fetching World Bank countries: {e}")
            return []
    
    def get_world_bank_indicators(self) -> List[Dict]:
        """Get list of all available indicators from World Bank"""
        url = "https://api.worldbank.org/v2/indicators"
        params = {'format': 'json', 'per_page': 1000}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return data[1] if len(data) > 1 else []
        except Exception as e:
            print(f"Error fetching World Bank indicators: {e}")
            return []
    
    def get_world_bank_data(self, country_code: str, indicator: str, 
                           start_year: str = "2010", end_year: str = "2024") -> Dict:
        """
        Get World Bank economic data
        
        Args:
            country_code: ISO2 code (SG, US, GB) or 'all' for all countries
            indicator: World Bank indicator code
            start_year: Start year (YYYY)
            end_year: End year (YYYY)
        """
        url = f"https://api.worldbank.org/v2/countries/{country_code}/indicators/{indicator}"
        params = {
            'format': 'json',
            'date': f"{start_year}:{end_year}",
            'per_page': 1000
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return {
                'source': 'World Bank',
                'country': country_code,
                'indicator': indicator,
                'data': data[1] if len(data) > 1 else []
            }
        except Exception as e:
            print(f"Error fetching World Bank data for {country_code} ({indicator}): {e}")
            return {'source': 'World Bank', 'error': str(e)}
    
    # ===========================================
    # FRED API (US Federal Reserve)
    # ===========================================
    
    def get_fred_data(self, series_id: str, start_date: str = None, end_date: str = None) -> Dict:
        """Get FRED economic data"""
        if not self.fred_api_key:
            return {'source': 'FRED', 'error': 'FRED API key required'}
        
        url = "https://api.stlouisfed.org/fred/series/observations"
        params = {
            'series_id': series_id,
            'api_key': self.fred_api_key,
            'file_type': 'json'
        }
        if start_date: params['observation_start'] = start_date
        if end_date: params['observation_end'] = end_date
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return {'source': 'FRED', 'series_id': series_id, 'data': response.json().get('observations', [])}
        except Exception as e:
            print(f"Error fetching FRED data: {e}")
            return {'source': 'FRED', 'error': str(e)}
    
    # ===========================================
    # COMBINED METHODS
    # ===========================================
    
    def get_country_overview(self, country_code: str) -> Dict:
        """
        Get comprehensive country data from available free sources
        
        Args:
            country_code: ISO2 country code (e.g., 'SG', 'US', 'GB')
        """
        overview = {
            'country': country_code,
            'timestamp': datetime.now().isoformat(),
            'sources': {}
        }
        
        # World Bank - Key Economic Indicators
        wb_indicators = {
            'GDP': 'NY.GDP.MKTP.CD',           # GDP (current US$)
            'GDP_Growth': 'NY.GDP.MKTP.KD.ZG', # GDP growth (annual %)
            'Population': 'SP.POP.TOTL',        # Total population
            'Unemployment': 'SL.UEM.TOTL.ZS',   # Unemployment (% of total labor force)
            'Inflation': 'FP.CPI.TOTL.ZG',      # Inflation, consumer prices (annual %)
            'Trade_Balance': 'BN.CAB.XOKA.CD'   # Current account balance (BoP, current US$)
        }
        
        wb_data = {}
        for name, indicator in wb_indicators.items():
            data = self.get_world_bank_data(country_code, indicator)
            wb_data[name] = data
            time.sleep(0.1)  # Be respectful to API
        
        overview['sources']['world_bank'] = wb_data
        
        # FRED data (only runs for US and if API key is available)
        if country_code.upper() == 'US' and self.fred_api_key:
            fred_series = {
                'GDP': 'GDP',
                'Unemployment_Rate': 'UNRATE',
                'CPI': 'CPIAUCSL',
                'Federal_Funds_Rate': 'FEDFUNDS'
            }
            fred_data = {}
            for name, series_id in fred_series.items():
                data = self.get_fred_data(series_id)
                fred_data[name] = data
                time.sleep(0.1)
            overview['sources']['fred'] = fred_data
        
        return overview
    
    def get_global_comparison(self, indicator: str, countries: List[str]) -> Dict:
        """Compare specific indicator across multiple countries"""
        comparison = {'indicator': indicator, 'countries': {}, 'timestamp': datetime.now().isoformat()}
        for country in countries:
            data = self.get_world_bank_data(country, indicator)
            comparison['countries'][country] = data
            time.sleep(0.1)
        return comparison
    
    def to_dataframe(self, data: Dict) -> pd.DataFrame:
        """Convert World Bank or FRED API response to pandas DataFrame"""
        try:
            source = data.get('source', '').lower()
            if source == 'world bank' or source == 'fred':
                df = pd.DataFrame(data.get('data', []))
                if not df.empty and 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    # FRED data has '.' for missing values, convert to numeric
                    if 'value' in df.columns:
                        df['value'] = pd.to_numeric(df['value'], errors='coerce')
                    df = df.sort_values('date', ascending=False)
                return df
            return pd.DataFrame(data)
        except Exception as e:
            print(f"Error converting to DataFrame: {e}")
            return pd.DataFrame()
# --- End of your FreeEconomicDataAPI Class ---


# --- Langgraph Setup for Economic Insights ---

# Define the state for our graph.
class EconomicAgentState(TypedDict):
    """Represents the state of our economic analysis workflow."""
    api_client: FreeEconomicDataAPI  # The API client instance
    country_code: str
    gdp_growth_df: Optional[pd.DataFrame]
    inflation_df: Optional[pd.DataFrame]
    gdp_growth_str: str  # String representation for LLM
    inflation_str: str   # String representation for LLM
    analysis_prompt: str
    llm_insights: str

# Node 1: Fetch economic data using the FreeEconomicDataAPI
def fetch_economic_data(state: EconomicAgentState) -> Dict:
    """
    Fetches GDP growth and inflation data for the specified country.
    """
    api_client = state["api_client"]
    country_code = state["country_code"]
    print(f"\n--- Fetching economic data for {country_code} ---")

    gdp_data_raw = api_client.get_world_bank_data(country_code, 'NY.GDP.MKTP.KD.ZG', start_year="2010", end_year="2024")
    inflation_data_raw = api_client.get_world_bank_data(country_code, 'FP.CPI.TOTL.ZG', start_year="2010", end_year="2024")

    gdp_df = api_client.to_dataframe(gdp_data_raw).dropna(subset=['value'])# Get top 15 non-null values
    inflation_df = api_client.to_dataframe(inflation_data_raw).dropna(subset=['value'])# Get top 15 non-null values

    print(f"Fetched GDP Growth data (top 5):\n{gdp_df[['date', 'value']].head()}")
    print(f"Fetched Inflation data (top 5):\n{inflation_df[['date', 'value']].head()}")

    return {
        "gdp_growth_df": gdp_df,
        "inflation_df": inflation_df
    }

# Node 2: Format data into a string for the LLM
def format_data_for_llm(state: EconomicAgentState) -> Dict:
    """
    Converts DataFrames into string representations suitable for the LLM.
    """
    gdp_df = state["gdp_growth_df"]
    inflation_df = state["inflation_df"]
    country_code = state["country_code"]
    print(f"\n--- Formatting data for LLM for {country_code} ---")

    gdp_str = ""
    if not gdp_df.empty:
        gdp_str = gdp_df[['date', 'value']].to_markdown(index=False)
    else:
        gdp_str = "No GDP Growth data available."

    inflation_str = ""
    if not inflation_df.empty:
        inflation_str = inflation_df[['date', 'value']].to_markdown(index=False)
    else:
        inflation_str = "No Inflation data available."

    print("Formatted GDP Growth snippet for LLM:\n", gdp_str.split('\n')[0] + '\n' + '\n'.join(gdp_str.split('\n')[1:6]) + '...' if len(gdp_str.split('\n')) > 6 else gdp_str)
    print("Formatted Inflation snippet for LLM:\n", inflation_str.split('\n')[0] + '\n' + '\n'.join(inflation_str.split('\n')[1:6]) + '...' if len(inflation_str.split('\n')) > 6 else inflation_str)

    return {
        "gdp_growth_str": gdp_str,
        "inflation_str": inflation_str
    }

# Node 3: Prepare the analysis prompt for the LLM
def prepare_analysis_prompt(state: EconomicAgentState) -> dict:
    """
    Formats the economic data strings into a structured prompt for the LLM.
    """
    gdp_data = state["gdp_growth_str"]
    inflation_data = state["inflation_str"]
    country_code = state["country_code"]
    
    prompt = (
        f"You are an expert economic analyst. Your task is to analyze the provided "
        f"Singaporean economic data and offer concise, actionable insights, "
        "identifying key trends, year-over-year changes, and potential implications. "
        "Focus on GDP growth and inflation figures for Singapore.\n\n"
        "--- Singapore GDP Growth (Annual %) ---\n"
        f"{gdp_data}\n\n"
        "--- Singapore Inflation (Annual %) ---\n"
        f"{inflation_data}\n\n"
        "Please provide your economic insights, highlighting: \n"
        "1. Key trends in GDP growth and inflation over the period (e.g., periods of high/low growth, rising/falling inflation).\n"
        "2. Notable positive or negative changes and specific years.\n"
        "3. Any apparent correlations between GDP and inflation (e.g., high growth with rising inflation).\n"
        "4. A brief overall assessment of Singapore's recent economic performance based on this data, mentioning potential future outlook.\n"
        "Your response should be structured with clear headings and bullet points, and easy to understand."
    )
    print("\n--- Prepared Prompt for LLM (truncated) ---")
    print(prompt[:800] + "..." if len(prompt) > 800 else prompt) # Print a truncated prompt for brevity
    return {"analysis_prompt": prompt}

# Node 4: Call the LLM (Groq) to get insights
def get_llm_economic_insights(state: EconomicAgentState) -> dict:
    """
    Calls the Groq LLM with the prepared prompt and returns its analysis.
    """
    # Initialize the ChatGroq model
    # You can choose different models like "llama3-8b-8192", "llama3-70b-8192", or "mixtral-8x7b-32768"
    # Adjust 'temperature' for creativity (0.0 for more factual, higher for more creative)
    llm = ChatGroq(model="meta-llama/llama-4-maverick-17b-128e-instruct", temperature=0.0)

    print("\n--- Calling Groq LLM for Insights ---")
    try:
        response = llm.invoke(state["analysis_prompt"])
        return {"llm_insights": response.content}
    except Exception as e:
        print(f"Error calling Groq API: {e}")
        print("Please ensure your GROQ_API_KEY is correctly set and you have network access.")
        return {"llm_insights": f"Error: Could not retrieve insights due to API call failure: {e}"}

# Build the Langgraph workflow
workflow = StateGraph(EconomicAgentState)

# Add nodes to the workflow
workflow.add_node("fetch_data", fetch_economic_data)
workflow.add_node("format_data", format_data_for_llm)
workflow.add_node("prepare_prompt", prepare_analysis_prompt)
workflow.add_node("get_insights", get_llm_economic_insights)

# Set the entry point for the graph
workflow.set_entry_point("fetch_data")

# Define the edges (transitions between nodes)
workflow.add_edge("fetch_data", "format_data")
workflow.add_edge("format_data", "prepare_prompt")
workflow.add_edge("prepare_prompt", "get_insights")
workflow.add_edge("get_insights", END) # The graph ends after getting insights

# Compile the graph into an executable application
app = workflow.compile()

# --- Main execution ---
if __name__ == "__main__":
    load_dotenv() # Load environment variables from .env file

    # --- 1. Set your Groq API Key ---
    # It's recommended to set your API key as an environment variable.
    # Example: GROQ_API_KEY="your_groq_api_key_here" in a .env file
    if "GROQ_API_KEY" not in os.environ:
        print("WARNING: GROQ_API_KEY environment variable not set.")
        print("Please set it before running the script (e.g., in a .env file or export GROQ_API_KEY='your_key')")
        print("You can get a key from: https://console.groq.com/keys")
        exit("Exiting: GROQ_API_KEY is required for the LLM to function.")

    print("=== Singapore Economic Insight Generator ===\n")

    # Initialize your FreeEconomicDataAPI
    fred_api_key = os.getenv("FRED_API_KEY") # FRED key is optional for World Bank data
    economic_api_client = FreeEconomicDataAPI(fred_api_key=fred_api_key)

    # Initial state for the Langgraph workflow
    initial_state = {
        "api_client": economic_api_client,
        "country_code": "SG", # Focusing on Singapore
        "gdp_growth_df": None,
        "inflation_df": None,
        "gdp_growth_str": "",
        "inflation_str": "",
        "analysis_prompt": "",
        "llm_insights": ""
    }

    try:
        # Run the Langgraph workflow
        final_state = app.invoke(initial_state)

        print("\n" + "="*40)
        print("=== Final Economic Insights for Singapore ===")
        print("="*40)
        print(final_state["llm_insights"])
        print("="*40)

    except Exception as e:
        print(f"\nAn error occurred during workflow execution: {e}")
        print("Please check your Groq API key, network connection, and API limits.")

