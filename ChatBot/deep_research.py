import os
import requests
import json

# --- Configuration ---
# The model with web search capabilities.
GEMINI_MODEL = 'gemini-2.5-flash-preview-05-20'
API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_MODEL}:generateContent"

# --- Core Functions ---

def get_api_key():
    """
    Retrieves the Gemini API key from environment variables.
    Exits the script if the key is not found.
    """
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("🔴 ERROR: GEMINI_API_KEY environment variable not set.")
        print("Please get your API key from Google AI Studio and set it as an environment variable.")
        print("For example, on Linux/macOS: export GEMINI_API_KEY='your_api_key_here'")
        print("On Windows: set GEMINI_API_KEY=your_api_key_here")
        exit(1)
    return api_key

def perform_research(topic: str, api_key: str) -> dict:
    """
    Calls the Gemini API to perform research on a given topic.

    Args:
        topic: The research topic.
        api_key: The Gemini API key.

    Returns:
        The JSON response from the API as a dictionary.
    """
    print(f"\n researching '{topic}'...")

    system_prompt = """
    You are an expert research analyst. Your task is to conduct a thorough and unbiased
    investigation into the provided topic using the available search tools.
    Synthesize your findings into a comprehensive, well-structured, and easy-to-understand summary.
    - Start with a clear overview of the topic.
    - Cover key aspects, recent developments, different perspectives, and important data points.
    - Conclude with a forward-looking statement or a summary of the main takeaways.
    - Ensure the tone is professional, informative, and objective.
    - Format the output for clear readability in a command-line interface. Use newlines for paragraphs.
    """

    payload = {
        "contents": [{"parts": [{"text": topic}]}],
        "tools": [{"google_search": {}}], # Enables the model to use Google Search
        "systemInstruction": {
            "parts": [{"text": system_prompt}]
        },
    }

    headers = {
        "Content-Type": "application/json",
    }
    
    params = { "key": api_key }

    try:
        response = requests.post(API_URL, params=params, headers=headers, json=payload, timeout=120)
        response.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"🔴 Network or API error occurred: {e}")
        return None

def display_results(api_response: dict):
    """
    Parses the API response and prints the research summary and sources.

    Args:
        api_response: The JSON response from the Gemini API.
    """
    if not api_response:
        print("Could not display results due to a prior error.")
        return

    try:
        # Extract the main text content
        candidate = api_response.get("candidates", [])[0]
        summary = candidate.get("content", {}).get("parts", [])[0].get("text", "No summary available.")

        # Extract sources from grounding metadata
        grounding_metadata = candidate.get("groundingMetadata", {})
        attributions = grounding_metadata.get("groundingAttributions", [])
        sources = [
            {"uri": attr.get("web", {}).get("uri"), "title": attr.get("web", {}).get("title")}
            for attr in attributions if attr.get("web")
        ]

        # Print the summary
        print("\n" + "="*80)
        print("🔬 AI RESEARCH SUMMARY")
        print("="*80)
        print(summary)

        # Print the sources
        if sources:
            print("\n" + "-"*80)
            print("📚 SOURCES")
            print("-"*80)
            for i, source in enumerate(sources, 1):
                print(f"[{i}] {source['title']}")
                print(f"    {source['uri']}")
        else:
            print("\nNo web sources were cited for this response.")
        
        print("\n" + "="*80)

    except (IndexError, KeyError, TypeError) as e:
        print("\n🔴 Error: Could not parse the API response.")
        print("The response structure might have changed or was incomplete.")
        print("Raw Response:")
        print(json.dumps(api_response, indent=2))


# --- Main Execution ---

if __name__ == "__main__":
    print("--- AI Deep Research Agent ---")
    api_key = get_api_key()
    
    try:
        while True:
            user_topic = input("\nEnter a research topic (or type 'exit' to quit): ")
            if user_topic.lower() == 'exit':
                break
            if not user_topic.strip():
                print("Please enter a topic.")
                continue

            response_data = perform_research(user_topic, api_key)
            display_results(response_data)

    except KeyboardInterrupt:
        print("\n\nExiting research agent. Goodbye!")
    
