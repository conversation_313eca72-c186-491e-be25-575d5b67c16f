{"cells": [{"cell_type": "code", "execution_count": null, "id": "b59567f4", "metadata": {}, "outputs": [], "source": ["import os\n", "from typing import List\n", "\n", "# To load environment variables from a .env file\n", "from dotenv import load_dotenv\n", "\n", "# LangChain components for interacting with the language model\n", "from langchain_groq import ChatGroq\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain.schema.output_parser import StrOutputParser\n", "\n", "# Tavily for performing the web search\n", "from tavily import TavilyClient\n", "\n", "def get_competitors(company: str, industry: str) -> List[str]:\n", "    \"\"\"\n", "    Finds and returns a list of competitors for a given company and industry.\n", "\n", "    This function performs two main steps:\n", "    1.  Uses the Tavily search API to find articles and text about the\n", "        company's competitive landscape.\n", "    2.  Feeds this text to a language model (LLM) with a specific prompt\n", "        to extract only the names of the competitors.\n", "\n", "    Args:\n", "        company: The name of the company to research.\n", "        industry: The industry or domain of the company.\n", "\n", "    Returns:\n", "        A list of strings, where each string is the name of a competitor.\n", "        Returns an empty list if no competitors are found.\n", "    \"\"\"\n", "    print(f\"\\n researching competitors for {company} in the {industry} industry...\")\n", "\n", "    # --- Step 1: Perform a targeted web search ---\n", "    print(\" searching the web for market data...\")\n", "    search_query = f\"key direct competitors of {company} in the {industry} market\"\n", "    \n", "    # Initialize the Tavily client and perform the search\n", "    api_key = os.environ.get('TAVILY_API_KEY')\n", "    if not api_key:\n", "        raise ValueError(\"TAVILY_API_KEY environment variable not set.\")\n", "    tavily_client = TavilyClient(api_key=api_key)\n", "    \n", "    try:\n", "        # We only need 2-3 good sources to find the top competitors\n", "        search_results = tavily_client.search(query=search_query, max_results=3)['results']\n", "        # Combine the content of the search results into a single string\n", "        search_context = \"\\n\\n\".join([result['content'] for result in search_results])\n", "    except Exception as e:\n", "        print(f\"Error during web search: {e}\")\n", "        return []\n", "\n", "    # --- Step 2: Use an LLM to extract competitor names from the search results ---\n", "    print(\" extracting competitor names from search results...\")\n", "\n", "    # The prompt is critical. It tells the LLM to act as an expert and format the output perfectly.\n", "    extraction_prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", \"You are an expert market analyst. Your sole task is to extract company names from a given text.\"),\n", "        (\"human\", \"\"\"Based on the following text, identify the main competitors of {company}.\n", "\n", "        <text>\n", "        {search_context}\n", "        </text>\n", "\n", "        Instructions:\n", "        1. Return ONLY a comma-separated list of the competitor names.\n", "        2. Do NOT include the original company, '{company}', in the list.\n", "        3. Do NOT add any introduction, explanation, or conclusion.\n", "        4. Example format: Competitor A, Competitor B, Competitor C\"\"\")\n", "    ])\n", "\n", "    # Initialize the language model\n", "    llm = ChatGroq(model_name='Gemma2-9b-it', api_key=os.getenv('GROQ_API_KEY'))\n", "\n", "    # Create and run the extraction chain\n", "    extraction_chain = extraction_prompt | llm | StrOutputParser()\n", "    competitor_string = extraction_chain.invoke({\n", "        \"company\": company,\n", "        \"search_context\": search_context\n", "    })\n", "\n", "    # --- Step 3: Clean up and return the list ---\n", "    if competitor_string:\n", "        # Split the string by commas and strip any extra whitespace from each name\n", "        competitors = [name.strip() for name in competitor_string.split(',') if name.strip()]\n", "        return competitors\n", "    else:\n", "        return []\n", "\n", "if __name__ == \"__main__\":\n", "    # Load API keys from your .env file\n", "    load_dotenv()\n", "\n", "    print(\"--- Competitor Finder ---\")\n", "    \n", "    try:\n", "        # Get input from the user\n", "        company_name = input(\"Enter the company name: \").strip()\n", "        industry_name = input(\"Enter the industry or domain: \").strip()\n", "\n", "        # Get the list of competitors\n", "        competitor_list = get_competitors(company_name, industry_name)\n", "\n", "        # Print the results\n", "        if competitor_list:\n", "            print(f\"\\nFound the following competitors for {company_name}:\")\n", "            for competitor in competitor_list:\n", "                print(f\"- {competitor}\")\n", "        else:\n", "            print(f\"\\nCould not find any direct competitors for {company_name} in the specified domain.\")\n", "\n", "    except ValueError as e:\n", "        print(f\"\\nError: {e}\")\n", "        print(\"Please make sure you have a .env file with TAVILY_API_KEY and GROQ_API_KEY.\")\n", "    except Exception as e:\n", "        print(f\"\\nAn unexpected error occurred: {e}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}