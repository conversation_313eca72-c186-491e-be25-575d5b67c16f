import boto3
import sys
import os
from typing import Dict, Any, List
from dotenv import load_dotenv

load_dotenv()

class BedrockRAGHandler:
    def __init__(self):
        self.region_name = os.getenv("AWS_REGION", "us-east-1")
        self.knowledge_base_id = os.getenv("BEDROCK_KNOWLEDGE_BASE_ID", "NZW3OFZAJP")
        self.model_arn = os.getenv("BEDROCK_MODEL_ARN", 
                                  "arn:aws:bedrock:us-east-1::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0")
        
        try:
            self.bedrock_agent_runtime = boto3.client(
                "bedrock-agent-runtime",
                region_name=self.region_name
            )
        except Exception as e:
            print(f"Error initializing Boto3 client: {e}")
            self.bedrock_agent_runtime = None

    def query_knowledge_base(self, query: str) -> Dict[str, Any]:
        """Query the Bedrock Knowledge Base and return structured response"""
        if not self.bedrock_agent_runtime:
            return {"error": "Bedrock client not initialized", "answer": "", "sources": []}
        
        try:
            response = self.bedrock_agent_runtime.retrieve_and_generate(
                input={'text': query},
                retrieveAndGenerateConfiguration={
                    'type': 'KNOWLEDGE_BASE',
                    'knowledgeBaseConfiguration': {
                        'knowledgeBaseId': self.knowledge_base_id,
                        'modelArn': self.model_arn
                    }
                }
            )
            
            answer = response['output']['text']
            citations = response.get('citations', [])
            sources = []
            
            for citation in citations:
                for reference in citation.get('retrievedReferences', []):
                    s3_uri = reference['location']['s3Location']['uri']
                    sources.append(s3_uri)
            
            return {
                "answer": answer,
                "sources": sources,
                "error": None
            }
            
        except Exception as e:
            return {
                "error": f"Error querying knowledge base: {e}",
                "answer": "",
                "sources": []
            }