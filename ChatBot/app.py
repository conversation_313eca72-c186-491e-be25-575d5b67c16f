# app.py

import streamlit as st
import asyncio
from datetime import datetime  # <-- THIS IS THE CORRECTED LINE
from market_analysis_generator import orchestrate_full_market_report, ALL_INDUSTRIES

# --- Helper function to display the report ---
def display_report(report_data):
    """Parses and displays the generated report data in a user-friendly format."""
    
    st.markdown("---")
    st.header(f"Full Market Report")
    # This line will now work correctly
    st.caption(f"Generated on: {datetime.fromisoformat(report_data['report_timestamp']).strftime('%Y-%m-%d %H:%M:%S')}")

    # 1. Economic Overview
    with st.expander("📈 Country Economic Overview", expanded=True):
        econ_overview = report_data.get("country_economic_overview", {})
        if econ_overview.get("status") == "failed":
            st.error(f"Failed to generate economic overview: {econ_overview.get('error')}")
        elif econ_overview.get("llm_economic_insights"):
            st.markdown(econ_overview["llm_economic_insights"])
        else:
            st.warning("No economic insights were generated.")

    # 2. Industry-Specific Analysis
    st.header("🏭 Industry & Company Analysis")
    
    industry_reports = report_data.get("industry_analysis", {}).get("individual_industry_reports", {})
    company_reports = report_data.get("company_insights", {})

    for industry_name, trend_data in industry_reports.items():
        with st.expander(f"**{industry_name}**"):
            
            # Mega Trends Section
            st.subheader("🌐 Mega Trends Analysis")
            if trend_data.get("status") == "failed":
                st.error(f"Failed to generate mega trends analysis: {trend_data.get('error')}")
            elif trend_data.get("executive_report_summary"):
                st.markdown(trend_data["executive_report_summary"])
            else:
                st.warning("No mega trends report was generated for this industry.")

            # Competitive Landscape Section
            st.subheader("🏢 Competitive Landscape")
            industry_company_insights = company_reports.get(industry_name, {})
            if not industry_company_insights or industry_company_insights.get("status") in ["no_primary_company_found", "failed"]:
                st.warning(f"Could not retrieve company insights for {industry_name}. Reason: {industry_company_insights.get('status', 'Unknown')}")
            else:
                for company_name, company_data in industry_company_insights.items():
                    if company_data.get("status") == "completed":
                        st.markdown(company_data["update_report"], unsafe_allow_html=True)
                        st.markdown("---")
                    else:
                        st.warning(f"Could not generate update for {company_name}. Reason: {company_data.get('error')}")


# --- Streamlit App Main Interface ---
st.set_page_config(page_title="Market Research Report Generator", layout="wide")

st.title("🤖 AI-Powered Market Research Report Generator")
st.markdown("This tool provides a comprehensive market analysis, including a country's economic overview, industry mega trends, and a competitive landscape analysis for key companies.")

# --- Sidebar for Inputs ---
with st.sidebar:
    st.header("Report Configuration")
    
    country_code = st.text_input("Enter Country Code (e.g., US, SG, DE)", "US").upper()
    
    # Industry selection
    select_all = st.checkbox("Select All Industries")
    if select_all:
        selected_industries = st.multiselect("Select Industries for Analysis", ALL_INDUSTRIES, default=ALL_INDUSTRIES)
    else:
        selected_industries = st.multiselect("Select Industries for Analysis", ALL_INDUSTRIES, default=["Data Centers & Artificial Intelligence", "Automotive"])

    st.markdown("---")
    # API Key Check
    keys_present = all([
        st.secrets.get("GROQ_API_KEY"),
        st.secrets.get("TAVILY_API_KEY"),
        st.secrets.get("GEMINI_API_KEY")
    ])
    if not keys_present:
        st.error("API Keys are missing! Please add GROQ, TAVILY, and GEMINI keys to your .streamlit/secrets.toml file.")
    else:
        st.success("API keys loaded successfully.")

# --- Main Page ---
if 'report_data' not in st.session_state:
    st.session_state.report_data = None

if st.sidebar.button("Generate Full Report", disabled=(not keys_present or not selected_industries or not country_code)):
    with st.spinner("Generating your comprehensive market report... This may take several minutes."):
        progress_bar = st.empty() # Placeholder for progress text
        
        def progress_callback(message):
            progress_bar.text(message)

        try:
            # Running the async orchestrator from the sync streamlit environment
            report = asyncio.run(orchestrate_full_market_report(
                country_code=country_code,
                industries_list=selected_industries,
                fred_api_key=st.secrets.get("FRED_API_KEY"),
                groq_api_key=st.secrets.get("GROQ_API_KEY"),
                tavily_api_key=st.secrets.get("TAVILY_API_KEY"),
                gemini_api_key=st.secrets.get("GEMINI_API_KEY"),
                progress_callback=progress_callback
            ))
            st.session_state.report_data = report
            progress_bar.empty() # Clear the progress text
            st.success("Report generation complete!")
        except Exception as e:
            st.error(f"An unexpected error occurred: {e}")
            st.session_state.report_data = None

# Display the report if it exists in the session state
if st.session_state.report_data:
    display_report(st.session_state.report_data)
else:
    st.info("Please configure your report in the sidebar and click 'Generate Full Report'.")