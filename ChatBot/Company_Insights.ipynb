{"cells": [{"cell_type": "code", "execution_count": 2, "id": "135cc4d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting broad search for recent updates and financial information on 'Tesla'...\n", "\n", "-> Scraping content from 15 potential sources...\n", "   Processing source 1/15: https://ir.tesla.com/press\n", "   [Error] Could not scrape https://ir.tesla.com/press: 403 Client Error: Forbidden for url: https://ir.tesla.com/press\n", "   Processing source 2/15: https://www.morningstar.com/news/business-wire/20250723861840/tesla-releases-second-quarter-2025-financial-results\n", "   Scraped 3217 characters from https://www.morningstar.com/news/business-wire/20250723861840/tesla-releases-second-quarter-2025-financial-results\n", "   Processing source 3/15: https://www.investors.com/news/tesla-stock-buy-signals-breakout/\n", "   Scraped 1836 characters from https://www.investors.com/news/tesla-stock-buy-signals-breakout/\n", "   Processing source 4/15: https://www.ttnews.com/articles/tesla-chair-musk-next-phase\n", "   Scraped 6315 characters from https://www.ttnews.com/articles/tesla-chair-musk-next-phase\n", "   Processing source 5/15: https://www.fool.com/investing/2025/09/14/is-it-finally-time-to-give-up-on-tesla/\n", "   Scraped 6994 characters from https://www.fool.com/investing/2025/09/14/is-it-finally-time-to-give-up-on-tesla/\n", "   Processing source 6/15: https://coincentral.com/tesla-tsla-stock-musk-claims-optimus-robots-will-drive-80-of-company-value/\n", "   Scraped 5245 characters from https://coincentral.com/tesla-tsla-stock-musk-claims-optimus-robots-will-drive-80-of-company-value/\n", "   Processing source 7/15: https://www.tesla.com/sites/default/files/downloads/TSLA-Q2-2025-Update.pdf\n", "   [Error] Could not scrape https://www.tesla.com/sites/default/files/downloads/TSLA-Q2-2025-Update.pdf: 403 Client Error: Forbidden for url: https://www.tesla.com/sites/default/files/downloads/TSLA-Q2-2025-Update.pdf\n", "   Processing source 8/15: https://www.teslaacessories.com/blogs/news/the-state-of-tesla-a-comprehensive-september-2025-market-technology-report?srsltid=AfmBOooMuJippACwI6NF2npwf8RpTEv45xT3eBYoBhHvmvG8yCrvKX5l\n", "   [Error] Could not scrape https://www.teslaacessories.com/blogs/news/the-state-of-tesla-a-comprehensive-september-2025-market-technology-report?srsltid=AfmBOooMuJippACwI6NF2npwf8RpTEv45xT3eBYoBhHvmvG8yCrvKX5l: 403 Client Error: Forbidden for url: https://www.teslaacessories.com/blogs/news/the-state-of-tesla-a-comprehensive-september-2025-market-technology-report?srsltid=AfmBOooMuJippACwI6NF2npwf8RpTEv45xT3eBYoBhHvmvG8yCrvKX5l\n", "   Processing source 9/15: https://ir.tesla.com/\n", "   [Error] Could not scrape https://ir.tesla.com/: 403 Client Error: Forbidden for url: https://ir.tesla.com/\n", "   Processing source 10/15: https://www.cnbc.com/quotes/TSLA\n", "   Scraped 3436 characters from https://www.cnbc.com/quotes/TSLA\n", "   Processing source 11/15: https://www.nasdaq.com/market-activity/stocks/tsla/earnings\n", "   Scraped 4696 characters from https://www.nasdaq.com/market-activity/stocks/tsla/earnings\n", "   Processing source 12/15: https://finance.yahoo.com/news/tesla-pivots-robots-investors-sales-094537687.html\n", "   Scraped 6456 characters from https://finance.yahoo.com/news/tesla-pivots-robots-investors-sales-094537687.html\n", "   Processing source 13/15: https://www.reuters.com/business/autos-transportation/teslas-german-plant-plans-lift-production-factory-boss-tells-dpa-news-agency-2025-09-14/\n", "   [Error] Could not scrape https://www.reuters.com/business/autos-transportation/teslas-german-plant-plans-lift-production-factory-boss-tells-dpa-news-agency-2025-09-14/: 401 Client Error: HTTP Forbidden for url: https://www.reuters.com/business/autos-transportation/teslas-german-plant-plans-lift-production-factory-boss-tells-dpa-news-agency-2025-09-14/\n", "   Processing source 14/15: https://www.teslarati.com/tesla-model-y-ownership-two-weeks-in-what-i-love-what-i-dont/\n", "   Scraped 16943 characters from https://www.teslarati.com/tesla-model-y-ownership-two-weeks-in-what-i-love-what-i-dont/\n", "   Processing source 15/15: https://www.youtube.com/watch?v=znF2cMMcoCM\n", "   Scraped 222 characters from https://www.youtube.com/watch?v=znF2cMMcoCM\n", "\n", "-> Generating company update for Tesla with Gemini LLM...\n", "   (Trimming content from 55370 to 30000 characters for LLM analysis)\n", "\n", "==================== TESLA COMPANY UPDATE ====================\n", "1) Tesla\n", "What’s new (last ~2–6 weeks)\n", "    •   Tesla released its Second Quarter 2025 financial results on July 23, 2025, reporting $1.2 billion GAAP net income and $1.4 billion non-GAAP net income, marking a \"seminal point\" in its transition. [Tesla Investor Relations, Morningstar]\n", "    •   The board revealed an unprecedented compensation agreement for Elon Musk, potentially worth around $1 trillion, which includes benchmarks for robotaxi expansion, EV deliveries, and market value, and will be put to a shareholder vote in November. [TT News, The Motley Fool]\n", "    •   CEO <PERSON><PERSON> claimed on X (September 1, 2025) that Optimus humanoid robots will eventually drive approximately 80% of Tesla's value, shifting the primary narrative from robotaxis and electric vehicles. [CoinCentral]\n", "    •   Tesla (TSLA) stock experienced a significant rally (6-7% on September 12-13, 2025), breaking out past a buy point, with market observers attributing the surge to expanded robotaxi testing and Musk's robotics predictions. [Investors.com, CoinCentral, CNBC]\n", "    •   The company plans to launch a stripped-down Model Y crossover during the fourth quarter to address affordability, alongside continued development of the Cybercab for 2026 production, which is envisioned as a sub-$30,000 autonomous vehicle without human controls. [The Motley Fool]\n", "\n", "Why it matters (insights)\n", "1.  **Strategic Pivot to AI/Robotics:** The proposed $1 trillion compensation package for Elon Musk, tied to ambitious benchmarks including robotaxi expansion and significant market value growth, coupled with Musk's explicit claim that Optimus robots will constitute 80% of Tesla's value, signals a profound strategic pivot. This indicates Tesla is increasingly betting its future on becoming an AI and robotics powerhouse, rather than solely an EV manufacturer, a high-stakes move amidst slowing EV sales and intensifying competition. [Connects Musk's compensation, Optimus claim, and general market conditions]\n", "2.  **Market Sentiment vs. Core Business Challenges:** Despite projections for a nearly 30% decline in 2025 earnings and ongoing struggles with stagnating EV sales and intense competition (especially from Chinese manufacturers), Tesla's stock recently rallied. This suggests that market valuation is heavily influenced by the long-term, speculative potential of its AI and robotics ventures, rather than immediate automotive fundamentals. This creates a disconnect where the stock's premium valuation leaves little room for disappointment in these nascent technologies. [Connects stock performance, 2025 earnings projections, and EV market challenges]\n", "3.  **Leadership and Governance Focus:** The board's strong endorsement of <PERSON><PERSON> as a \"generational leader\" and the unprecedented compensation package highlight the company's deep reliance on his vision for its future. However, this also brings increased scrutiny on corporate governance, succession planning, and the potential for Musk's increased control (aiming for 25% stake) to influence strategic direction, particularly given his involvement in multiple other ventures. [Connects compensation package, <PERSON><PERSON>'s comments, and Musk's public statements]\n", "\n", "Watchlist for Q3–Q4 2025:\n", "    •   **Shareholder Vote on Musk's Compensation Package:** Scheduled for November, this vote will be critical in determining Musk's future incentives and potential for increased control, impacting investor confidence and long-term strategic alignment.\n", "    •   **Q3 2025 Earnings Report:** Expected around October 21, 2025, this will provide updated financial performance metrics, insights into EV sales trends, profitability, and any further details on capital allocation towards AI and robotics initiatives.\n", "    •   **Launch of Stripped-Down Model Y:** The introduction of a more affordable Model Y in Q4 2025 will be a key indicator of Tesla's ability to stimulate demand and compete on price in a challenging EV market, particularly following the expiration of federal tax credits.\n", "    •   **Updates on Optimus and Cybercab Development:** Any new demonstrations, production timelines, or technological advancements for the Optimus humanoid robot and the Cybercab (robotaxi) will be closely watched, as these are increasingly positioned as the primary drivers of Tesla's future valuation.\n", "======================================================================\n", "\n", "######################################################################\n", "\n"]}], "source": ["import os\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from urllib.parse import urlparse\n", "from tavily import TavilyClient\n", "import google.generativeai as genai\n", "import datetime\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "# --- Configuration & Blocklists ---\n", "# DOMAIN_BLOCKLIST is commented out to allow scraping from news sites, etc.\n", "# DEFAULT_KEYWORDS and get_dynamic_keywords_from_llm are removed as the new\n", "# LLM approach directly generates the analytical report from broad content.\n", "\n", "# --- LLM Functions ---\n", "def generate_company_update(company_name, text_content, tavily_search_results):\n", "    \"\"\"\n", "    Uses Gemini LLM to analyze aggregated text and search results\n", "    to generate a company update in three sections: What's new, Why it matters, and Watchlist.\n", "    \"\"\"\n", "    print(f\"\\n-> Generating company update for {company_name} with Gemini LLM...\")\n", "    max_chars = 30000  # Increased max_chars for more extensive content to provide more context to the LLM\n", "    if len(text_content) > max_chars:\n", "        print(f\"   (Trimming content from {len(text_content)} to {max_chars} characters for LLM analysis)\")\n", "        text_content = text_content[:max_chars]\n", "\n", "    # Format Tavily search results for context to help the LLM identify sources and specific events\n", "    context_string = \"\"\n", "    if tavily_search_results:\n", "        context_string = \"\\n\\n--- Top Search Results for Context (Titles, Snippets, and URLs to help identify sources) ---\\n\"\n", "        # Using top 5-7 results to provide concentrated source hints to the LLM\n", "        for i, res in enumerate(tavily_search_results[:7]):\n", "            source_domain = urlparse(res['url']).netloc.replace('www.', '')\n", "            context_string += f\"[{i+1}] Title: {res['title']}\\n\"\n", "            context_string += f\"    Source: {source_domain}\\n\"\n", "            context_string += f\"    Snippet: {res['content']}\\n\\n\"\n", "        context_string += \"--------------------------------------------------------------------------\\n\\n\"\n", "\n", "    try:\n", "        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "\n", "        # Dynamically determine the watchlist period for the current year\n", "        current_year = datetime.datetime.now().year\n", "        # Assuming we want a watchlist for the current and upcoming quarters\n", "        # The example used Q3-Q4 2025; let's make it dynamic for the current year's remaining quarters.\n", "        current_month = datetime.datetime.now().month\n", "        if current_month <= 9: # If it's up to September, include Q3 and Q4\n", "            watchlist_period = f\"Q3–Q4 {current_year}\"\n", "        else: # If it's October-December, focus on Q4 and potentially Q1 of next year\n", "            watchlist_period = f\"Q4 {current_year} and Q1 {current_year + 1}\"\n", "\n", "        prompt = f\"\"\"\n", "        You are a highly experienced financial analyst and market observer.\n", "        Based on the following aggregated text from various reliable news articles, company announcements, investor pages, and other sources related to '{company_name}', generate a concise company update.\n", "\n", "        Here is some additional context from top search results which may help you identify specific news outlets or official announcements for sourcing recent developments:\n", "        {context_string}\n", "\n", "        Aggregated Scraped Text for detailed analysis (contains more comprehensive content from the sources):\n", "        ---\n", "        {text_content}\n", "        ---\n", "\n", "        Structure your update *exactly* into the following three sections, using bullet points as shown in the example.\n", "        For \"What's new\", aim to identify 3-5 distinct, significant developments for '{company_name}' from the *past approximately 2 to 6 weeks*.\n", "        For \"Why it matters\", provide 2-3 key insights, explaining the significance of the news.\n", "        For \"Watchlist\", list 2-4 key events, trends, or data points to monitor for the remainder of {current_year} (specifically {watchlist_period}).\n", "\n", "        Format the output precisely as follows:\n", "\n", "        1) {company_name}\n", "        What’s new (last ~2–6 weeks)\n", "        {'    '}• [Brief summary of a significant recent development]. [Source, e.g., Benzinga, MarketWatch, Company Investor Relations, Official Press Release]\n", "        {'    '}• [Brief summary of another significant recent development]. [Source]\n", "        {'    '}• ... (continue with 1-3 more bullet points)\n", "\n", "        Why it matters (insights)\n", "        1. [Insight 1]: Explain the significance, market impact, or strategic implications of the news. Connect the dots between recent developments and potential future trajectory. (If relevant, mention implications for suppliers or related industries.) [Cite specific news points or sources if an insight is directly tied to one piece of news; otherwise, this is your analytical synthesis]\n", "        2. [Insight 2]: ... (provide 1-2 more key insights)\n", "\n", "        Watchlist for {watchlist_period}:\n", "        {'    '}• [Key event or trend to monitor, e.g., upcoming earnings call, new product launch, regulatory decision, market trend]. [Brief explanation or reason for monitoring]\n", "        {'    '}• [Another key item to monitor].\n", "        {'    '}• ... (continue with 1-2 more bullet points)\n", "\n", "        IMPORTANT GUIDELINES:\n", "        - Prioritize information that is recent, impactful, and directly relevant to {company_name}.\n", "        - Do not invent facts, figures, or news. If specific details are not present, make a reasonable inference based on the overall context or avoid making a definitive claim.\n", "        - For sources in \"What's new\", refer to the provided 'Top Search Results for Context' and the aggregated text to infer the most likely news outlet, company official source (e.g., 'Company Investor Relations', 'Press Release'), or use a more general attribution like 'Multiple News Outlets' if widely reported. Aim for specific names if possible.\n", "        - Ensure all sections are distinct and clearly address the prompt's requirements for content and timeframe.\n", "        - Maintain a professional, analytical tone suitable for a financial analyst.\n", "        \"\"\"\n", "        response = model.generate_content([{\"role\": \"user\", \"parts\": [prompt]}], generation_config={\"temperature\": 0.3})\n", "        return response.candidates[0].content.parts[0].text\n", "    except Exception as e:\n", "        return f\"[<PERSON><PERSON><PERSON>] Could not generate company update from Gemini LLM: {e}\"\n", "\n", "# --- Scraping Functions ---\n", "def simple_scrape(url):\n", "    \"\"\"Scrapes the text content of a single URL.\"\"\"\n", "    try:\n", "        session = requests.Session()\n", "        # Using a common User-Agent to mimic a browser\n", "        session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'})\n", "        response = session.get(url, timeout=10)\n", "        response.raise_for_status()  # Raise an HTTPError for bad responses (4xx or 5xx)\n", "        soup = BeautifulSoup(response.text, 'html.parser')\n", "        # Remove script, style, header, footer, and nav elements for cleaner text content\n", "        for script_or_style in soup(['script', 'style', 'header', 'footer', 'nav', 'aside']):\n", "            script_or_style.extract()\n", "        text = ' '.join(soup.get_text().split()) # Normalize whitespace\n", "        print(f\"   Scraped {len(text)} characters from {url}\")\n", "        return text\n", "    except requests.RequestException as e:\n", "        print(f\"   [<PERSON><PERSON><PERSON>] Could not scrape {url}: {e}\")\n", "        return \"\"\n", "\n", "# --- Main Workflow Function ---\n", "def main_workflow(company_name):\n", "    \"\"\"\n", "    Full workflow: search broadly for recent news and financial information,\n", "    scrape multiple relevant sources, aggregate content, and generate a\n", "    company update with \"What's new\", \"Why it matters\", and \"Watchlist\".\n", "    \"\"\"\n", "    print(f\"Starting broad search for recent updates and financial information on '{company_name}'...\")\n", "    try:\n", "        tavily_client = TavilyClient(api_key=os.getenv('TAVILY_API_KEY'))\n", "        # Broaden the search query to emphasize recent news, announcements, and market developments\n", "        search_query = f\"{company_name} latest news OR recent financial update OR investor announcements OR market developments\"\n", "        # Increase max_results to provide more diverse content for the LLM's analysis\n", "        response = tavily_client.search(search_query, search_depth=\"advanced\", max_results=15)\n", "        if not response or 'results' not in response:\n", "            print(\"Tavily search failed or returned no results.\")\n", "            return\n", "        search_results = response['results']\n", "    except Exception as e:\n", "        print(f\"An error occurred during Tavily search: {e}\")\n", "        return\n", "\n", "    # Filter out results without a URL before proceeding\n", "    potential_sources = [res['url'] for res in search_results if res.get('url')]\n", "\n", "    if not potential_sources:\n", "        print(\"No potential sources found after initial search.\")\n", "        return\n", "\n", "    print(f\"\\n-> Scraping content from {len(potential_sources)} potential sources...\")\n", "    full_scraped_corpus = \"\"\n", "    for i, url in enumerate(potential_sources):\n", "        print(f\"   Processing source {i+1}/{len(potential_sources)}: {url}\")\n", "        scraped_text = simple_scrape(url)\n", "        if scraped_text:\n", "            full_scraped_corpus += scraped_text + \" \"\n", "\n", "    if not full_scraped_corpus:\n", "        print(\"\\n❌ Could not gather any text content from the search results.\")\n", "        return\n", "\n", "    if full_scraped_corpus and len(full_scraped_corpus) > 500:\n", "        # Pass the original search_results to help the LLM with source attribution\n", "        update_report = generate_company_update(company_name, full_scraped_corpus, search_results)\n", "        print(\"\\n\" + \"=\"*20 + f\" {company_name.upper()} COMPANY UPDATE \" + \"=\"*20)\n", "        print(update_report)\n", "        print(\"=\"*70)\n", "    else:\n", "        print(\"\\n❌ Could not gather enough text content from the websites to generate a detailed company update.\")\n", "\n", "# --- Example Usage ---\n", "if __name__ == \"__main__\":\n", "    # Ensure you have your API keys set as environment variables.\n", "    # For example:\n", "    # os.environ['TAVILY_API_KEY'] = 'YOUR_TAVILY_API_KEY'\n", "    # os.environ['GEMINI_API_KEY'] = 'YOUR_GEMINI_API_KEY'\n", "\n", "    # Example 1: Tesla (to match the provided output example's company)\n", "    main_workflow(\"Tesla\")\n", "    print(\"\\n\" + \"#\"*70 + \"\\n\")\n", "\n", "    # You can uncomment these to test with other companies\n", "    # main_workflow(\"NVIDIA\")\n", "    # print(\"\\n\" + \"#\"*70 + \"\\n\")\n", "\n", "    # main_workflow(\"Siemens\")\n", "    # print(\"\\n\" + \"#\"*70 + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "1cac6883", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai_project_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}