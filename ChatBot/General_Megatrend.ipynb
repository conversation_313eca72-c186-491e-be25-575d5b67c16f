{"cells": [{"cell_type": "code", "execution_count": null, "id": "31ac895d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Running in notebook environment...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Starting mega trends analysis for 21 industries...\n", "INFO:__main__:Analyzing industry: 5G and Wireless Equipment\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 Starting comprehensive mega trends analysis...\n", "📊 Analyzing 21 industries...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 1.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Aerospace\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 43.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Appliances\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 43.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Automation & Control\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 23.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Automotive\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 45.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 24.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Autosport\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 42.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Commercial & Industrial Vehicles\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 20.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Connected Home\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 18.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Data Centers & Artificial Intelligence\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 25.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Defense & Military\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 16.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Energy Solutions\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 17.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: E-mobility\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 15.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Industrial Machinery\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 21.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Intelligent Buildings & Smart Cities\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 19.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: IoT Connectivity\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 14.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Analyzing industry: Medical Technologies\n", "INFO:httpx:HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:groq._base_client:Retrying request to /openai/v1/chat/completions in 20.000000 seconds\n"]}], "source": ["import os\n", "import asyncio\n", "import json\n", "from typing import Dict, List, Any, TypedDict\n", "from datetime import datetime\n", "import logging\n", "\n", "# Core dependencies\n", "from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_groq import ChatGroq\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "from langgraph.graph import StateGraph, END\n", "from langgraph.graph.message import add_messages\n", "from langgraph.prebuilt import ToolNode\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "class AgentState(TypedDict):\n", "    messages: List[BaseMessage]\n", "    current_industry: str\n", "    industry_trends: Dict[str, Any]\n", "    completed_industries: List[str]\n", "    final_report: str\n", "\n", "class IndustryTrendsAgent:\n", "    \"\"\"\n", "    Advanced LLM Agent for analyzing mega trends for a SINGLE industry\n", "    using Groq LLM, <PERSON>ly search, and LangGraph orchestration\n", "    \"\"\"\n", "    \n", "    def __init__(self, groq_api_key: str, tavily_api_key: str, target_industry: str): # Added target_industry\n", "        self.groq_api_key = groq_api_key\n", "        self.tavily_api_key = tavily_api_key\n", "        self.target_industry = target_industry # Store the single target industry\n", "        \n", "        # Initialize Groq LLM\n", "        self.llm = ChatGroq(\n", "            temperature=0.1,\n", "            model_name=\"meta-llama/llama-4-maverick-17b-128e-instruct\",  # High-performance model\n", "            groq_api_key=groq_api_key,\n", "            max_tokens=4000\n", "        )\n", "        \n", "        # Initialize Tavily search tool\n", "        self.search_tool = TavilySearchResults(\n", "            api_key=tavily_api_key,\n", "            max_results=5,\n", "            search_depth=\"advanced\",\n", "            include_answer=True,\n", "            include_raw_content=False\n", "        )\n", "        \n", "        # The 'industries' list is removed as we focus on a single one\n", "        \n", "        # Build the agent graph\n", "        self.graph = self._build_graph()\n", "    \n", "    def _build_graph(self) -> StateGraph:\n", "        \"\"\"Build the LangGraph workflow\"\"\"\n", "        \n", "        def route_next_industry(state: AgentState) -> str:\n", "            \"\"\"Determine if the target industry has been analyzed or move to report generation.\"\"\"\n", "            # If the target industry is in completed_industries, move to final report\n", "            if self.target_industry in state.get(\"completed_industries\", []):\n", "                return \"generate_final_report\"\n", "            # Otherwise, analyze the industry (this should only happen once)\n", "            return \"analyze_industry\"\n", "        \n", "        def analyze_industry_node(state: AgentState) -> AgentState:\n", "            \"\"\"Analyze trends for the specific target industry\"\"\"\n", "            current_industry = self.target_industry\n", "            \n", "            # Check if this industry has already been completed to prevent re-analysis\n", "            if current_industry in state.get(\"completed_industries\", []):\n", "                logger.info(f\"Industry '{current_industry}' already analyzed. Skipping.\")\n", "                return state\n", "                \n", "            logger.info(f\"Analyzing industry: {current_industry}\")\n", "            \n", "            # Search for recent trends and developments\n", "            search_query = f\"{current_industry} industry mega trends 2024 2025 technology innovation market forecast\"\n", "            search_results = self.search_tool.invoke(search_query)\n", "            \n", "            # Create analysis prompt\n", "            analysis_prompt = ChatPromptTemplate.from_messages([\n", "                (\"system\", \"\"\"You are an expert industry analyst specializing in identifying mega trends for a specific technology or industrial sector. \n", "\n", "Your task is to analyze the provided search results and identify the top 3-5 mega trends for the specified industry.\n", "\n", "For each trend, provide:\n", "1. Trend Name (concise and descriptive)\n", "2. Description (2-3 sentences explaining the trend)\n", "3. Impact Level (High/Medium/Low)\n", "4. Timeline (Near-term: 1-2 years, Medium-term: 3-5 years, Long-term: 5+ years)\n", "5. Key Drivers (main factors driving this trend)\n", "6. Potential Disruptions (what this trend might disrupt or transform)\n", "\n", "Focus on:\n", "- Technological innovations\n", "- Market shifts\n", "- Regulatory changes\n", "- Consumer behavior changes\n", "- Sustainability initiatives\n", "- Digital transformation aspects\n", "\n", "Be specific, actionable, and forward-looking in your analysis.\"\"\"),\n", "                (\"human\", \"\"\"Industry: {industry}\n", "\n", "Search Results:\n", "{search_results}\n", "\n", "Please provide a comprehensive mega trends analysis for this industry.\"\"\")\n", "            ])\n", "            \n", "            # Generate analysis\n", "            chain = analysis_prompt | self.llm\n", "            response = chain.invoke({\n", "                \"industry\": current_industry,\n", "                \"search_results\": json.dumps(search_results, indent=2)\n", "            })\n", "            \n", "            # Update state\n", "            industry_trends = state.get(\"industry_trends\", {})\n", "            industry_trends[current_industry] = {\n", "                \"analysis\": response.content,\n", "                \"search_data\": search_results,\n", "                \"timestamp\": datetime.now().isoformat()\n", "            }\n", "            \n", "            completed_industries = state.get(\"completed_industries\", [])\n", "            completed_industries.append(current_industry)\n", "            \n", "            messages = state.get(\"messages\", [])\n", "            messages.append(AIMessage(content=f\"Completed analysis for {current_industry}\"))\n", "            \n", "            return {\n", "                **state,\n", "                \"current_industry\": current_industry,\n", "                \"industry_trends\": industry_trends,\n", "                \"completed_industries\": completed_industries,\n", "                \"messages\": messages\n", "            }\n", "        \n", "        def generate_final_report_node(state: AgentState) -> AgentState:\n", "            \"\"\"Generate comprehensive final report for the single industry\"\"\"\n", "            logger.info(\"Generating final comprehensive report for the target industry...\")\n", "            \n", "            # Create executive summary prompt\n", "            summary_prompt = ChatPromptTemplate.from_messages([\n", "                (\"system\", \"\"\"You are an executive consultant creating a comprehensive mega trends report for a specific industry.\n", "\n", "Create a structured executive report that includes:\n", "\n", "1. EXECUTIVE SUMMARY\n", "   - Overview of key patterns and developments in the industry\n", "   - Key transformational themes\n", "   - Strategic implications\n", "\n", "2. INDUSTRY MEGA TRENDS\n", "   - Identify 3-5 overarching trends specific to this industry.\n", "   - For each trend, elaborate on its drivers, impact, and timeline as detailed in the analysis.\n", "\n", "3. STRATEGIC RECOMMENDATIONS\n", "   - Investment priorities specific to this industry\n", "   - Risk considerations\n", "   - Innovation opportunities\n", "\n", "4. TIMELINE OUTLOOK\n", "   - Near-term priorities (1-2 years)\n", "   - Medium-term transformations (3-5 years)\n", "   - Long-term disruptions (5+ years)\n", "\n", "Make the report professional, actionable, and suitable for C-level executives.\"\"\"),\n", "                (\"human\", \"\"\"Based on the comprehensive industry analysis below, create an executive mega trends report:\n", "\n", "{industry_analyses}\n", "\n", "Total industries analyzed: {industry_count}\"\"\")\n", "            ])\n", "            \n", "            # Compile all industry analyses (which will be just one)\n", "            industry_analyses = \"\"\n", "            for industry, data in state[\"industry_trends\"].items():\n", "                industry_analyses += f\"\\n\\n=== {industry.upper()} ===\\n{data['analysis']}\\n\"\n", "            \n", "            # Generate final report\n", "            chain = summary_prompt | self.llm\n", "            final_report = chain.invoke({\n", "                \"industry_analyses\": industry_analyses,\n", "                \"industry_count\": len(state[\"industry_trends\"]) # Will be 1\n", "            })\n", "            \n", "            messages = state.get(\"messages\", [])\n", "            messages.append(AIMessage(content=\"Final comprehensive report generated successfully!\"))\n", "            \n", "            return {\n", "                **state,\n", "                \"final_report\": final_report.content,\n", "                \"messages\": messages\n", "            }\n", "        \n", "        # Build the graph\n", "        workflow = StateGraph(AgentState)\n", "        \n", "        # Add nodes\n", "        workflow.add_node(\"analyze_industry\", analyze_industry_node)\n", "        workflow.add_node(\"generate_final_report\", generate_final_report_node)\n", "        \n", "        # Add edges\n", "        workflow.set_entry_point(\"analyze_industry\")\n", "        workflow.add_conditional_edges(\n", "            \"analyze_industry\",\n", "            route_next_industry,\n", "            {\n", "                \"analyze_industry\": \"analyze_industry\", # This edge means it could re-run analyze_industry, but our node logic prevents it\n", "                \"generate_final_report\": \"generate_final_report\"\n", "            }\n", "        )\n", "        workflow.add_edge(\"generate_final_report\", END)\n", "        \n", "        return workflow.compile()\n", "    \n", "    async def analyze_all_industries(self) -> Dict[str, Any]:\n", "        \"\"\"Run complete analysis for the target industry\"\"\"\n", "        \n", "        logger.info(f\"Starting mega trends analysis for industry: {self.target_industry}...\")\n", "        \n", "        # Initialize state\n", "        initial_state: AgentState = {\n", "            \"messages\": [HumanMessage(content=f\"Starting comprehensive mega trends analysis for {self.target_industry}\")],\n", "            \"current_industry\": \"\",\n", "            \"industry_trends\": {},\n", "            \"completed_industries\": [],\n", "            \"final_report\": \"\"\n", "        }\n", "        \n", "        # Execute the graph\n", "        # The graph will run 'analyze_industry' once, then transition to 'generate_final_report'\n", "        result = await self.graph.ainvoke(initial_state)\n", "        \n", "        return result\n", "    \n", "    def save_results(self, results: Dict[str, Any], filename: str = None) -> str:\n", "        \"\"\"Save analysis results to file\"\"\"\n", "        \n", "        if filename is None:\n", "            filename = f\"mega_trends_analysis_{self.target_industry.replace(' ', '_').lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json\"\n", "        \n", "        # Prepare data for saving\n", "        save_data = {\n", "            \"analysis_metadata\": {\n", "                \"timestamp\": datetime.now().isoformat(),\n", "                \"target_industry\": self.target_industry, # Changed\n", "                \"completed_industries_count\": len(results.get(\"completed_industries\", [])), # Changed name\n", "                \"industries_analyzed\": results.get(\"completed_industries\", [])\n", "            },\n", "            \"executive_report\": results.get(\"final_report\", \"\"),\n", "            \"industry_details\": results.get(\"industry_trends\", {}),\n", "            \"full_results\": results\n", "        }\n", "        \n", "        with open(filename, 'w', encoding='utf-8') as f:\n", "            json.dump(save_data, f, indent=2, ensure_ascii=False)\n", "        \n", "        logger.info(f\"Results saved to {filename}\")\n", "        return filename\n", "    \n", "    def print_executive_summary(self, results: Dict[str, Any]):\n", "        \"\"\"Print formatted executive summary\"\"\"\n", "        \n", "        print(\"\\n\" + \"=\"*80)\n", "        print(f\"MEGA TRENDS ANALYSIS - EXECUTIVE SUMMARY FOR: {self.target_industry.upper()}\") # Changed\n", "        print(\"=\"*80)\n", "        \n", "        if results.get(\"final_report\"):\n", "            print(results[\"final_report\"])\n", "        \n", "        print(f\"\\n{'='*80}\")\n", "        print(f\"Analysis completed for {len(results.get('completed_industries', []))} industry\") # Changed for singular\n", "        print(f\"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "        print(\"=\"*80)\n", "\n", "\n", "# Usage Example and Main Execution\n", "async def main():\n", "    \"\"\"Main execution function\"\"\"\n", "    \n", "    # Configuration - Replace with your actual API keys\n", "    GROQ_API_KEY = os.getenv(\"GROQ_API_KEY\", \"your-groq-api-key-here\")\n", "    TAVILY_API_KEY = os.getenv(\"TAVILY_API_KEY\", \"your-tavily-api-key-here\")\n", "    \n", "    if GROQ_API_KEY == \"your-groq-api-key-here\" or TAVILY_API_KEY == \"your-tavily-api-key-here\":\n", "        print(\"⚠️  Please set your GROQ_API_KEY and TAVILY_API_KEY environment variables\")\n", "        print(\"   export GROQ_API_KEY='your-actual-groq-key'\")\n", "        print(\"   export TAVILY_API_KEY='your-actual-tavily-key'\")\n", "        return\n", "    \n", "    # Define the single industry to analyze\n", "    target_industry_name = \"Data Centers & Artificial Intelligence\" # Specify your desired industry here\n", "    \n", "    # Initialize the agent with the target industry\n", "    agent = IndustryTrendsAgent(GROQ_API_KEY, TAVILY_API_KEY, target_industry_name)\n", "    \n", "    try:\n", "        # Run the complete analysis\n", "        print(f\"🚀 Starting comprehensive mega trends analysis for '{target_industry_name}'...\")\n", "        \n", "        results = await agent.analyze_all_industries()\n", "        \n", "        # Display results\n", "        agent.print_executive_summary(results)\n", "        \n", "        # Save detailed results\n", "        filename = agent.save_results(results)\n", "        print(f\"💾 Detailed results saved to: {filename}\")\n", "        \n", "        return results\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Analysis failed: {str(e)}\")\n", "        raise\n", "\n", "\n", "def run_analysis():\n", "    \"\"\"\n", "    Synchronous wrapper function that handles event loop detection\n", "    Works in both Jupyter notebooks and regular Python scripts\n", "    \"\"\"\n", "    try:\n", "        # Try to get existing event loop\n", "        loop = asyncio.get_event_loop()\n", "        if loop.is_running():\n", "            # We're in a notebook or already have a running loop\n", "            print(\"🔄 Running in notebook environment...\")\n", "            import nest_asyncio\n", "            nest_asyncio.apply()\n", "            return asyncio.create_task(main())\n", "        else:\n", "            # No running loop, we can use asyncio.run()\n", "            return asyncio.run(main())\n", "    except RuntimeError:\n", "        # No event loop exists, create one\n", "        return asyncio.run(main())\n", "\n", "\n", "def run_quick_demo():\n", "    \"\"\"\n", "    Quick demo function for testing the agent with a single industry\n", "    \"\"\"\n", "    async def demo():\n", "        # Configuration\n", "        GROQ_API_KEY = os.getenv(\"GROQ_API_KEY\", \"your-groq-api-key-here\")\n", "        TAVILY_API_KEY = os.getenv(\"TAVILY_API_KEY\", \"your-tavily-api-key-here\")\n", "        \n", "        if GROQ_API_KEY == \"your-groq-api-key-here\" or TAVILY_API_KEY == \"your-tavily-api-key-here\":\n", "            print(\"⚠️  Please set your API keys as environment variables\")\n", "            return\n", "        \n", "        # Define the single industry for the demo\n", "        demo_industry_name = \"E-mobility\" # Specify a different industry for demo if desired\n", "        \n", "        # Create agent for the single demo industry\n", "        agent = IndustryTrendsAgent(GROQ_API_KEY, TAVILY_API_KEY, demo_industry_name)\n", "        \n", "        print(f\"🚀 Running quick demo for industry: '{demo_industry_name}'...\")\n", "        results = await agent.analyze_all_industries()\n", "        agent.print_executive_summary(results)\n", "        \n", "        return results\n", "    \n", "    try:\n", "        loop = asyncio.get_event_loop()\n", "        if loop.is_running():\n", "            import nest_asyncio\n", "            nest_asyncio.apply()\n", "            return asyncio.create_task(demo())\n", "        else:\n", "            return asyncio.run(demo())\n", "    except RuntimeError:\n", "        return asyncio.run(demo())\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # For script execution\n", "    run_analysis()"]}, {"cell_type": "code", "execution_count": null, "id": "1e1dc5b5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai_project_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}