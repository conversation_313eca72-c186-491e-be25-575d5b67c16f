# %%
import os
import json
import getpass
import operator
import base64
from enum import Enum
from dataclasses import dataclass, fields
from typing import Any, Dict, List, Optional, TypedDict, Annotated
from typing_extensions import Literal
from io import BytesIO
import streamlit as st
# Data modeling
from pydantic import BaseModel, Field

# Image processing
from PIL import Image
import requests
from IPython.display import Image, display, Markdown

# LangChain and related tools
from langsmith import traceable
from langchain_groq import ChatGroq
from langchain_core.messages import SystemMessage, HumanMessage, ToolMessage, AIMessage
from langchain_core.runnables import (
    RunnableConfig,
    RunnableParallel,
    RunnableLambda,
    RunnableBranch
)
from langchain.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser
from langchain_core.tools import tool, StructuredTool
from langchain_core import tools

# Financial data
import yfinance as yf
import pandas as pd

# Web search and HTML parsing
import requests
from bs4 import BeautifulSoup
from urllib.request import urlopen
from tavily import TavilyClient

# Visualization
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.colors as pc
import plotly.io as pio

# YouTube API
import googleapiclient.discovery
import googleapiclient.errors

# State graph
from langgraph.graph import StateGraph, START, END

# Environment variables
from dotenv import load_dotenv

# OpenAI
from openai import OpenAI
from langchain_nvidia_ai_endpoints import ChatNVIDIA
from curl_cffi import requests

import logging
from typing import Optional, Dict, Any

# RAG functionality
from bedrock_rag import BedrockRAGHandler

# %%
session = requests.Session(impersonate="chrome")

# %%
load_dotenv()

llm = ChatGroq(model_name='Gemma2-9b-it', api_key = os.getenv('GROQ_API_KEY'))

# %%
query_writer_instruction_web = """You are a Market Research Analyst conducting research on {company}. 
Your goal is to generate a targeted web search query related to {company}, focusing on its market position, competitors, consumer perception, or industry trends.

<COMPANY>
{company}
</COMPANY>

<FORMAT>
Format your response as a JSON object with ALL three of these exact keys:
   - "query": The actual search query string
   - "aspect": The specific aspect of {company}'s market or business being researched
   - "rationale": Brief explanation of why this query is relevant for understanding {company}'s market environment
</FORMAT>

<EXAMPLE>
Example output:
{{
    "query": "Tesla competitive analysis in the electric vehicle market 2025",
    "aspect": "competitive landscape",
    "rationale": "To understand Tesla’s positioning against rivals and assess threats and opportunities in the EV industry"
}}
</EXAMPLE>

Provide your response in JSON format:
"""


# %%
summarizer_instruction_web = """<GOAL>
Generate a high-quality summary of the web search results, focusing on {company}'s market environment, industry trends, and competitive dynamics.
</GOAL>

<REQUIREMENTS>
When creating a NEW summary:
1. Highlight key findings about {company}, including market potential, customer sentiment, industry growth, or competitor strategies.
2. Ensure the summary is concise, actionable, and useful for evaluating {company}'s strategic position.

When EXTENDING an existing summary:
1. Read the existing summary and new search results carefully.
2. Compare the new information with the existing summary.
3. For each new piece of information:
    a. If it adds depth to an existing point (e.g., a competitor update), integrate it smoothly.
    b. If it introduces a new aspect (e.g., regulatory impact, partnerships), add it as a separate paragraph.
    c. Ignore irrelevant information unrelated to {company}.
4. Ensure the updated summary presents a well-rounded view of {company}'s market situation.
5. Verify the final output differs from the original while providing more depth.

<FORMATTING>
- Start directly with the updated summary, without preamble or titles. Do not use XML tags in the output.
</FORMATTING>
"""


# %%
reflection_instructions_web = """You are a Market Research Analyst analyzing a summary about {company}.

<GOAL>
1. Identify missing details or areas that need deeper exploration about {company}.
2. Generate a follow-up question to expand knowledge about {company}'s market environment, competitors, or growth opportunities.
3. Focus on competitive landscape, industry risks, consumer behavior, or regulatory factors not fully covered in the summary.
</GOAL>

<REQUIREMENTS>
Ensure the follow-up question is self-contained and provides necessary context for a web search.
</REQUIREMENTS>

<FORMAT>
Format your response as a JSON object with these exact keys:
- "knowledge_gap": Describe what important market information is missing or unclear.
- "follow_up_query": Write a specific question to address this gap.
</FORMAT>

<EXAMPLE>
Example output:
{{
    "knowledge_gap": "The summary does not mention how Apple is adapting its strategy to growing competition in the wearable technology sector.",
    "follow_up_query": "What strategies is Apple using to maintain market share in the wearable technology industry against competitors like Samsung and Fitbit?"
}}
</EXAMPLE>

Provide your analysis in JSON format:
"""


# %%
class State(TypedDict):
    route: Literal['Web_query', 'Normal_query', 'Financial_Analysis', 'Plot_Graph'] = Field(None)
    research_topic: str
    search_query: str
    web_research_results: List[str]
    sources_gathered: List[str]
    research_loop_count: int
    running_summary: str
    image: list[str]
    image_processed: bool
    messages: List[Any]  # This will continue to be the working messages (possibly enhanced)
    original_messages: List[Any]  # New field to store original messages
    plot_type: Optional[str]
    ticker: Optional[str]
    plot_json: Optional[str]

# %%
def fetch_stock_data(ticker, period="1y"):
    stock = yf.Ticker(ticker, session = session)
    return stock.history(period=period)

def fetch_balance(ticker, tp="Annual"):
    ticker_obj = yf.Ticker(ticker, session = session)
    bs = ticker_obj.balance_sheet if tp == "Annual" else ticker_obj.quarterly_balance_sheet
    return bs.loc[:, bs.isna().mean() < 0.5]

# Plotting functions
def plot_candles_stick(df, title=""):
    fig = go.Figure(data=[go.Candlestick(x=df.index,
                open=df['Open'],
                high=df['High'],
                low=df['Low'],
                close=df['Close'])])
    fig.update_layout(title=title)
    return fig

def plot_balance(df, ticker="", currency=""):
    df.columns = pd.to_datetime(df.columns).strftime('%b %d, %Y')
    components = {
        'Total Assets': {'color': 'forestgreen', 'name': 'Assets'},
        'Stockholders Equity': {'color': 'CornflowerBlue', 'name': "Stockholder's Equity"},
        'Total Liabilities Net Minority Interest': {'color': 'tomato', 'name': "Total Liabilities"},
    }
    
    fig = go.Figure()
    for component in components:
        if component == 'Total Assets':
            fig.add_trace(go.Bar(
                x=[df.columns, ['Assets'] * len(df.columns)],
                y=df.loc[component],
                name=components[component]['name'],
                marker=dict(color=components[component]['color'])
            ))
        else:
            fig.add_trace(go.Bar(
                x=[df.columns, ['L+E'] * len(df.columns)],
                y=df.loc[component],
                name=components[component]['name'],
                marker=dict(color=components[component]['color'])
            ))

    offset = 0.03 * df.loc['Total Assets'].max()
    for i, date in enumerate(df.columns):
        fig.add_annotation(
            x=[date, "Assets"],
            y=df.loc['Total Assets', date] / 2,
            text=str(round(df.loc['Total Assets', date] / 1e9, 1)) + 'B',
            showarrow=False,
            font=dict(size=12, color="black"),
            align="center"
        )
        percentage = round((df.loc['Total Liabilities Net Minority Interest', date] / df.loc['Total Assets', date]) * 100, 1)
        fig.add_annotation(
            x=[date, "L+E"],
            y=df.loc['Stockholders Equity', date] + df.loc['Total Liabilities Net Minority Interest', date] / 2,
            text=str(percentage) + '%',
            showarrow=False,
            font=dict(size=12, color="black"),
            align="center"
        )
        if i > 0:
            percentage = round((df.loc['Total Assets'].iloc[i] / df.loc['Total Assets'].iloc[i - 1] - 1) * 100, 1)
            sign = '+' if percentage >= 0 else ''
            fig.add_annotation(
                x=[date, "Assets"],
                y=df.loc['Total Assets', date] + offset,
                text=sign + str(percentage) + '%',
                showarrow=False,
                font=dict(size=12, color="black"),
                align="center"
            )

    fig.update_layout(
        barmode='stack',
        title=f'Accounting Balance: {ticker}',
        xaxis_title='Year',
        yaxis_title=f'Amount (in {currency})',
        legend_title='Balance components',
    )
    return fig

def plot_assets(df, ticker="", currency=""):
    assets = {
        'Current Assets': {
            'Cash Cash Equivalents And Short Term Investments': {},
            'Receivables': {},
            'Prepaid Assets': None,
            'Inventory': {},
            'Hedging Assets Current': None,
            'Other Current Assets': None
        },
        'Total Non Current Assets': {
            'Net PPE': {},
            'Goodwill And Other Intangible Assets': {},
            'Investments And Advances': {},
            'Investment Properties': None,
            'Other Non Current Assets': None
        }
    }

    fig = make_subplots(
        rows=1, cols=2,
        shared_yaxes=True,
        horizontal_spacing=0.05,
        subplot_titles=['Current Assets', 'Non-Current Assets']
    )

    colors = pc.sequential.Blugrn[::-1]
    i = 0
    for component in assets['Current Assets']:
        if component in df.index:
            fig.add_trace(go.Bar(
                x=df.columns,
                y=df.loc[component],
                name=component,
                marker=dict(color=colors[i]),
                legendgroup='Current Assets',
                showlegend=True
            ), row=1, col=1)
            i += 1

    colors = pc.sequential.Purp[::-1]
    i = 0
    for component in assets['Total Non Current Assets']:
        if component in df.index:
            fig.add_trace(go.Bar(
                x=df.columns,
                y=df.loc[component],
                name=component,
                marker=dict(color=colors[i]),
                legendgroup='Non-current Assets',
                showlegend=True
            ), row=1, col=2)
            i += 1

    offset = 0.03 * max(df.loc['Current Assets'].max(), df.loc['Total Non Current Assets'].max())
    for i, date in enumerate(df.columns):
        fig.add_annotation(
            x=date,
            y=df.loc['Current Assets', date] + offset,
            text=str(round(df.loc['Current Assets', date] / 1e9, 1)) + 'B',
            showarrow=False,
            font=dict(size=12, color="black"),
            align="center",
            row=1, col=1
        )
        fig.add_annotation(
            x=date,
            y=df.loc['Total Non Current Assets', date] + offset,
            text=str(round(df.loc['Total Non Current Assets', date] / 1e9, 1)) + 'B',
            showarrow=False,
            font=dict(size=12, color="black"),
            align="center",
            row=1, col=2
        )

    fig.update_layout(
        barmode='stack',
        title=f'Assets: {ticker}',
        xaxis1=dict(title='Date', type='date', tickvals=df.columns),
        xaxis2=dict(title='Date', type='date', tickvals=df.columns),
        yaxis_title=f'Amount (in {currency})',
        legend_title='Asset Components',
    )
    return fig

# %%
class TickerRetrievalTool:
    def __init__(self, logger: logging.Logger = None):
        """
        Initialize the Ticker Retrieval Tool
        
        :param logger: Optional logger for tracking tool operations
        """
        self.logger = logger or logging.getLogger(__name__)
        self.yfinance_url = "https://query2.finance.yahoo.com/v1/finance/search"
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'

    def get_ticker(self, company_name: str, country: str) -> Optional[str]:
        """
        Retrieve stock ticker for a given company and country
        
        :param company_name: Name of the company to search
        :param country: Country of the stock exchange
        :return: Stock ticker symbol or None if not found
        """
        try:
            params = {
                "q": company_name, 
                "quotes_count": 5, 
                "country": country
            }
            
            # Make the request
            response = requests.get(
                url=self.yfinance_url, 
                params=params, 
                headers={'User-Agent': self.user_agent},
                timeout=10  # Add timeout to prevent hanging
            )
            
            # Raise an exception for bad HTTP responses
            response.raise_for_status()
            
            # Parse JSON response
            data = response.json()
            
            # Handle different country-specific logic
            if country.upper() == 'INDIA':
                for quote in data.get('quotes', []):
                    if quote.get('exchange') == 'NSI':
                        return quote.get('symbol')
            else:
                # For other countries, return the first matching quote
                return data['quotes'][0]['symbol']
        
        except requests.RequestException as e:
            self.logger.error(f"Network error retrieving ticker: {e}")
            return None
        
        except (KeyError, IndexError) as e:
            self.logger.error(f"No ticker found for {company_name} in {country}: {e}")
            return None
        
        except Exception as e:
            self.logger.error(f"Unexpected error in ticker retrieval: {e}")
            return None

    def tool_description(self) -> Dict[str, Any]:
        """
        Provide a description of the tool for agent integration
        
        :return: Dictionary describing the tool's capabilities
        """
        return {
            "name": "stock_ticker_retrieval",
            "description": "Retrieves stock ticker symbols for companies across different countries",
            "parameters": {
                "type": "object",
                "properties": {
                    "company_name": {
                        "type": "string",
                        "description": "Full or partial name of the company"
                    },
                    "country": {
                        "type": "string", 
                        "description": "Country of the stock exchange (e.g., 'India', 'US')"
                    }
                },
                "required": ["company_name", "country"]
            }
        }

def get_company_country(query):
    user_prompt = query
    prompt_get_country_company = """You are an expert at extracting precise company and country information from user queries. Follow these guidelines carefully:

                                    Extraction Rules:
                                    1. Identify the specific company name mentioned in the query
                                    2. Determine the country of origin for the identified company
                                    3. For index-related queries, use these specific mappings:
                                    - NIFTY50 → ['^NSEI', 'India']
                                    - NIFTY100 → ['^CNX100', 'India']
                                    - NIFTY MIDCAP 150 → ['NIFTYMIDCAP150.NS', 'India']

                                    Output Format Requirements:
                                    - Always respond in a strict JSON format
                                    - Use double quotes for keys and string values
                                    - Ensure no trailing commas
                                    - Keys must be exactly: "company" and "country"

                                    Example Outputs:
                                    - For "Tell me about Apple": 
                                    {"company": "Apple Inc.", "country": "United States"}
                                    - For "NIFTY50 performance": 
                                    {"company": "^NSEI", "country": "India"}

                                    Your task: Extract the company and country from the following query: 
                            """

    final_prompt = prompt_get_country_company + user_prompt
    #print(final_prompt)
    response = llm.invoke(final_prompt)
    #print(response.content)
    data = json.loads(response.content)
    company = data['company']
    country = data['country']
    #print(response.content)
    return [company, country]


def parse_query(state: State) -> State:
    """Parse the user query to determine plot type and ticker"""
    query = state["research_topic"].lower()
    #print('In parse query: ',query)
    data = get_company_country(query)
    #print(query)
    company = data[0]
    country = data[1]
    #print(company, country)
    ticker_tool = TickerRetrievalTool()
    ticker = ticker_tool.get_ticker(company, country)
    #print(ticker)
    if "candlestick" in query:
        return {"plot_type": "candlestick", "ticker": ticker}
    elif "balance" in query:
        return {"plot_type": "balance", "ticker": ticker}
    elif "assets" in query:
        return {"plot_type": "assets", "ticker": ticker}
    else:
    #    print('Returning NONE')
        return {"plot_type": None, "ticker": None}
    
def generate_plot(state: State) -> State:
    """Generate the appropriate plot based on the parsed query"""
    if not state["plot_type"] or not state["ticker"]:
        return {"response": "I can generate candlestick charts, balance sheets, or assets visualizations. Please specify what you'd like to see (e.g., 'Show me a candlestick chart for AAPL')"}
    
    ticker = state["ticker"]
    plot_type = state["plot_type"]
    #print('In generate_plot\t')
    try:
        if plot_type == "candlestick":
            df = fetch_stock_data(ticker)
            fig = plot_candles_stick(df, title=f"{ticker} Candlestick Chart")
        elif plot_type == "balance":
            df = fetch_balance(ticker)
            fig = plot_balance(df, ticker=ticker, currency="INR")
        elif plot_type == "assets":
            df = fetch_balance(ticker)
            fig = plot_assets(df, ticker=ticker, currency="INR")
        
        plot_json = fig.to_json()
        #print("Print in the generate plot\n",plot_json,'\n')
        return {"plot_json": plot_json}
    
    except Exception as e:
        #print('Returning error.', str(e))
        return {"response": f"Error generating plot: {str(e)}"}
    
def format_response(state: State) -> State:
    """Format the final response"""
    #print('In format_response:\t')
    if state.get("plot_json"):
        description = f"Here is the {state['plot_type']} plot for {state['ticker']}"
        return {"running_summary": description, "plot_json": state["plot_json"]}
    elif state.get("response"):
        return {"running_summary": state["response"]}
    else:
        #print('Something went wrong...')
        return {"running_summary": "Something went wrong while processing your request"}

# %%
def create_initial_state(user_query: str, image: list[str] = []) -> State:
    return {
        "route": None,
        "research_topic": user_query,
        "search_query": "",
        "web_research_results": [],
        "sources_gathered": [],
        "research_loop_count": 0,
        "running_summary": "",
        "image": image,
        "image_processed": False,
        "messages": [HumanMessage(content=user_query)],  # Working messages
        "original_messages": [HumanMessage(content=user_query)],  # Store original message
        "plot_type": None,
        "ticker": None,
        "plot_json": None
    }

# %%
class Route_First_Step(BaseModel):
    step: Literal['Web_query', 'Normal_query', 'Financial_Analysis', 'Plot_Graph', 'RAG_query'] = Field(
        None,
        description="""
        You are a market research analyst routing a user's query to the appropriate processing pipeline. Analyze the user's input and determine the best route based on their intent, keywords, and the system's capabilities. Choose one of the following routes:

        - 'Web_query': For queries requiring broad research from web sources on financial investments or finance-related topics. This route generates targeted search queries, summarizes web results, and identifies knowledge gaps for further exploration. Look for:
          - Keywords like "research," "trends," "best," "strategies," "outlook," "how to," "risks," "regulations," or open-ended questions about finance.
          - Topics like investment options, market trends, or financial strategies without a specific ticker or data point.
          - Queries needing external data beyond immediate knowledge or specific company metrics.
        - 'Normal_query': For straightforward questions answerable with existing knowledge (e.g., definitions, explanations, or simple facts). Look for "what is," "explain," "define," or general curiosity without specific data, visualization, or research needs.
        - 'Financial_Analysis': For queries needing precise financial data or analysis about a specific company, using tools for:
          - "address" (company address), "employees" (full-time employees), "close price"/"last price" (last close price), "EBITDA," "debt" (total debt), "revenue" (total revenue), "debt to equity" (debt-to-equity ratio).
          - Requires a ticker (e.g., AAPL, MSFT) AND one of the above keywords.
        - 'Plot_Graph': For queries requesting visualizations or charts, supporting:
          - "candlestick" (candlestick chart), "balance" (balance sheet visualization), "assets" (assets visualization).
          - Requires a ticker (e.g., AAPL, TSLA) AND one of the above plot-related keywords or general visualization terms like "chart," "graph," "visualize," "show me."
        - 'RAG_query': For queries that can be answered using the knowledge base or document repository. This route uses Retrieval-Augmented Generation to find relevant information from stored documents. Look for:
          - Keywords like "rag," "knowledge base," "documents," "search documents," "find in documents," or "what does the knowledge base say about"
          - Questions that might be answered by specific documents or stored knowledge
          - Queries asking for detailed information that might be in company documents, reports, or knowledge repositories

        Instructions:
        1. Carefully analyze the query for intent, keywords, and the presence of a ticker symbol (e.g., AAPL, MSFT).
        2. For 'Web_query':
           - Route here if the query seeks broad financial insights, trends, or strategies (e.g., "best index funds," "market trends 2025") without a ticker or specific data point.
           - Also route here for complex, research-oriented questions needing web data (e.g., "risks of ETF investing," "regulations on crypto").
           - Do NOT route here if the query targets a specific company’s data or visualization.
        3. For 'Financial_Analysis':
           - Route here if the query mentions a ticker AND asks for specific data like "address," "employees," "close price," "EBITDA," "debt," "revenue," or "debt to equity."
           - Do NOT route here if the query asks for a chart or broad research.
        4. For 'Plot_Graph':
           - Route here if the query mentions a ticker AND includes "candlestick," "balance," "assets," or general visualization terms like "chart," "graph," "visualize," "show me."
           - If no specific plot type is mentioned (e.g., just "chart" with a ticker), still route to 'Plot_Graph.'
        5. For 'RAG_query':
           - Route here if the query explicitly mentions "rag," "knowledge base," "documents," "search documents," or similar terms.
           - Also route here for questions that seem to ask for specific information that might be stored in documents or knowledge repositories.
           - Use this route when the user wants to search through stored documents or knowledge bases.
        6. If the query is vague but finance-related, default to 'Normal_query' unless it fits another category more precisely.
        7. Return your choice as a structured JSON object with the key "step."

        Examples:
        - Input: "What are the best index funds for 2025?"
          Output: {"step": "Web_query"} (Broad research on investment options)
        - Input: "What are the risks of investing in ETFs?"
          Output: {"step": "Web_query"} (Research-oriented, no ticker)
        - Input: "What is a P/E ratio?"
          Output: {"step": "Normal_query"} (General explanation)
        - Input: "What is the total debt of AAPL?"
          Output: {"step": "Financial_Analysis"} (Ticker + specific data: total_debt)
        - Input: "How many employees does MSFT have?"
          Output: {"step": "Financial_Analysis"} (Ticker + specific data: fulltime_employees)
        - Input: "Show me a candlestick chart for TSLA"
          Output: {"step": "Plot_Graph"} (Ticker + plot type: candlestick)
        - Input: "What’s the last close price of GOOGL?"
          Output: {"step": "Financial_Analysis"} (Ticker + specific data: last_close_price)
        - Input: "Visualize the balance sheet for AMZN"
          Output: {"step": "Plot_Graph"} (Ticker + plot type: balance)
        - Input: "Show me the assets for FB"
          Output: {"step": "Plot_Graph"} (Ticker + plot type: assets)
        - Input: "What are the latest market trends for 2025?"
          Output: {"step": "Web_query"} (Broad research, no ticker)
        - Input: "Chart for NVDA"
          Output: {"step": "Plot_Graph"} (Ticker + general visualization)
        - Input: "Search the knowledge base for information about risk management"
          Output: {"step": "RAG_query"} (Knowledge base search)
        - Input: "What does the documentation say about portfolio optimization?"
          Output: {"step": "RAG_query"} (Document search)
        - Input: "Find documents about ESG investing"
          Output: {"step": "RAG_query"} (Document retrieval)

        User Query: {query}
        """
    )# %%

# %%
class SearchAPI(Enum):
    PERPLEXITY = "perplexity"
    TAVILY = "tavily"
    DUCKDUCKGO = "duckduckgo"

@dataclass(kw_only=True)
class Configuration:
    max_web_research_loops: int = int("3")
    search_api: SearchAPI = SearchAPI("tavily")
    fetch_full_page: bool = "False".lower() in ("true", "1", "t")
    ollama_base_url: str = "http://localhost:11434/"

    @classmethod
    def from_runnable_config(cls, config: Optional[RunnableConfig] = None) -> "Configuration":
        configurable = config["configurable"] if config and "configurable" in config else {}
        values: dict[str, Any] = {
            f.name: configurable.get(f.name)
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})

# %%
@traceable
def tavily_search(query, include_raw_content=True, max_results=3):
    api_key = os.environ['TAVILY_API_KEY']
    #api_key = st.secrets.REST.TAVILY_API_KEY
    if not api_key:
        raise ValueError("TAVILY_API_KEY environment variable is not set")
    tavily_client = TavilyClient(api_key=api_key)
    return tavily_client.search(query, max_results=max_results, include_raw_content=include_raw_content)

def deduplicate_and_format_sources(search_response, max_tokens_per_source, include_raw_content=False):
    if isinstance(search_response, dict):
        sources_list = search_response['results']
    elif isinstance(search_response, list):
        sources_list = []
        for response in search_response:
            if isinstance(response, dict) and 'results' in response:
                sources_list.extend(response['results'])
            else:
                sources_list.extend(response)
    else:
        raise ValueError("Input must be either a dict with 'results' or a list of search results")

    unique_sources = {}
    for source in sources_list:
        if source['url'] not in unique_sources:
            unique_sources[source['url']] = source

    formatted_text = "Sources:\n\n"
    for source in unique_sources.values():
        formatted_text += f"Source {source['title']}:\n===\n"
        formatted_text += f"URL: {source['url']}\n===\n"
        formatted_text += f"Most relevant content from source: {source['content']}\n===\n"
        if include_raw_content:
            char_limit = max_tokens_per_source * 4
            raw_content = source.get('raw_content', '') or ''
            if len(raw_content) > char_limit:
                raw_content = raw_content[:char_limit] + "... [truncated]"
            formatted_text += f"Full source content limited to {max_tokens_per_source} tokens: {raw_content}\n\n"

    return formatted_text.strip()

def format_sources(search_results):
    return '\n'.join(
        f"* {source['title']} : {source['url']}"
        for source in search_results['results']
    )

# %%
def generate_query(state: State, config: RunnableConfig):
    prompt = query_writer_instruction_web.format(company=state["research_topic"]) + "\nGenerate a query for web search:"
    result = llm.invoke(prompt)
    output_text = result.content.strip()
    try:
        query_data = json.loads(output_text)
        return {"search_query": query_data['query']}
    except (json.JSONDecodeError, KeyError) as e:
        #print(f"Error parsing JSON: {e}")
        return {"search_query": f"comprehensive analysis of {state['research_topic']}"}

def web_research(state: State, config: RunnableConfig):
    configurable = Configuration.from_runnable_config(config)
    search_api = configurable.search_api.value if isinstance(configurable.search_api, Enum) is False else configurable.search_api.value
    if search_api == "tavily":
        search_results = tavily_search(state["search_query"], include_raw_content=True, max_results=1)
        search_str = deduplicate_and_format_sources(search_results, max_tokens_per_source=1000, include_raw_content=True)
    else:
        raise ValueError(f"Unsupported search API: {configurable.search_api}")
    return {
        "sources_gathered": [format_sources(search_results)],
        "research_loop_count": state["research_loop_count"] + 1,
        "web_research_results": [search_str]
    }

# %%
def summarize_sources(state: State, config: RunnableConfig):
    existing_summary = state['running_summary']
    most_recent_web_research = state['web_research_results'][-1]
    
    # (Content for human_message_content remains the same)
    if existing_summary:
        human_message_content = (
            f"<User Input> \n {state['research_topic']} \n <User Input>\n\n"
            f"<Existing Summary> \n {existing_summary} \n <Existing Summary>\n\n"
            f"<New Search Results> \n {most_recent_web_research} \n <New Search Results>"
        )
    else:
        human_message_content = (
            f"<User Input> \n {state['research_topic']} \n <User Input>\n\n"
            f"<Search Results> \n {most_recent_web_research} \n <Search Results>"
        )

    # FIX: Added .format() to insert the company name into the prompt
    prompt = summarizer_instruction_web.format(company=state['research_topic']) + "\n" + human_message_content
    result = llm.invoke(prompt)
    running_summary = result.content
    while "<think>" in running_summary and "</think>" in running_summary:
        start = running_summary.find("<think>")
        end = running_summary.find("</think>") + len("</think>")
        running_summary = running_summary[:start] + running_summary[end:]
    return {"running_summary": running_summary}

def reflect_on_summary(state: State, config: RunnableConfig):
    # FIX: Changed finance_topic to company to match the prompt template
    prompt = reflection_instructions_web.format(company=state['research_topic']) \
             + "\nIdentify a knowledge gap and generate a follow-up web search query based on our existing knowledge: " \
             + state['running_summary']
    result = llm.invoke(prompt)
    output_text = result.content.strip()
    try:
        follow_up_query = json.loads(output_text)
    except json.JSONDecodeError:
        #print("Error: Could not decode JSON from reflect_on_summary. Response was:", output_text)
        follow_up_query = {"follow_up_query": f"Tell me more about {state['research_topic']}"}
    query = follow_up_query.get('follow_up_query')
    if not query:
        return {"search_query": f"Tell me more about {state['research_topic']}"}
    return {"search_query": query}

def finalize_summary(state: State):
    all_sources = "\n".join(source for source in state['sources_gathered'])
    final_summary = f"{state['running_summary']}\n\n### Sources:\n{all_sources}"
    final_message = HumanMessage(content=final_summary)
    return {
        "running_summary": final_summary,
        "messages": [final_message],
        "original_messages": state["original_messages"]  # Preserve original messages
    }

def route_research(state: State, config: RunnableConfig) -> Literal["finalize_summary", "web_research"]:
    configurable = Configuration.from_runnable_config(config)
    if state['research_loop_count'] < configurable.max_web_research_loops:
        return "web_research"
    return "finalize_summary"


# %%
@tool
def company_address(ticker: str) -> str:
    """
    Returns company address for input ticker.
    e.g. company_address: AAPL
    Returns company address for ticker AAPL which is stock ticker for Apple Inc.
    """
    ticker_obj = yf.Ticker(ticker, session = session)
    info = ticker_obj.get_info()

    return " ".join([info[key] for key in ['address1','city','state','zip','country']])

@tool
def fulltime_employees(ticker: str) -> int:
    """
    Returns fulltime employees count for input ticker.
    e.g. company_address: MSFT
    Returns fulltime employees count for ticker MSFT which is stock ticker for Microsoft.
    """
    ticker_obj = yf.Ticker(ticker, session = session)
    info = ticker_obj.get_info()

    return info['fullTimeEmployees']

@tool
def last_close_price(ticker: str) -> float:
    """
    Returns last close price for input ticker.
    e.g. company_address: MSFT
    Returns last close price for ticker MSFT which is stock ticker for Microsoft.
    """
    ticker_obj = yf.Ticker(ticker, session = session)
    info = ticker_obj.get_info()

    return info['previousClose']

@tool
def EBITDA(ticker: str) -> float:
    """
    Returns EBITDA for input ticker.
    e.g. company_address: AAPL
    Returns EBITDA for ticker AAPL which is stock ticker for Apple Inc.
    """
    ticker_obj = yf.Ticker(ticker, session = session)
    info = ticker_obj.get_info()

    return info['ebitda']

@tool
def total_debt(ticker: str) -> float:
    """
    Returns total debt for input ticker.
    e.g. company_address: AAPL
    Returns total debt for ticker AAPL which is stock ticker for Apple Inc.
    """
    ticker_obj = yf.Ticker(ticker, session = session)
    info = ticker_obj.get_info()

    return info['totalDebt']

@tool
def total_revenue(ticker: str) -> float:
    """
    Returns total revenue for input ticker.
    e.g. company_address: MSFT
    Returns total revenue for ticker MSFT which is stock ticker for Microsoft.
    """
    ticker_obj = yf.Ticker(ticker, session = session)
    info = ticker_obj.get_info()

    return info['totalRevenue']

@tool
def debt_to_equity_ratio(ticker: str) -> float:
    """
    Returns debt to equity ratio for input ticker.
    e.g. company_address: AAPL
    Returns debt to equity ratio for ticker AAPL which is stock ticker for Apple Inc.
    """
    ticker_obj = yf.Ticker(ticker, session = session)
    info = ticker_obj.get_info()

    return info['debtToEquity']

finance_tools = [
    company_address,
    fulltime_employees,
    last_close_price,
    EBITDA,
    total_debt,
    total_revenue,
    debt_to_equity_ratio
]
finance_tool_map = {t.name: t for t in finance_tools}

# %%
llm_normal = llm

# %%
normal_query_prompt = """
You are a market analyst. Please answer the user's question based on what you know, don't make up anything. Make sure you elaborate it in such manner that it is easily understandable by anyone.
"""

# %%
def answer_normal_query(state: State):
    messages = state.get('messages', [])
    system_message = SystemMessage(content=normal_query_prompt + "\nFormat your response in Markdown.")
    response = llm_normal.invoke([system_message] + messages)
    markdown_response = f"{response.content}"
    return {
        "running_summary": markdown_response,
        "messages": [HumanMessage(content=markdown_response)],
        "original_messages": state["original_messages"]  # Preserve original messages
    }
llm_financial_analysis = llm.bind_tools(finance_tools, tool_choice='auto')

# %%
financial_analysis_prompt = """
You are a financial market analyst. You are given tools for accurate data.
"""

# %%
def call_llm(state: State):
    messages = state['messages']
    system_prompt = financial_analysis_prompt + "\nFormat your response in Markdown."
    messages = [SystemMessage(content=system_prompt)] + messages
    message = llm_financial_analysis.invoke(messages)
    return {'messages': [message]}

def exists_action(state: State):
    result = state['messages'][-1]
    return len(result.tool_calls) > 0

def take_action(state: State):
    tool_calls = state['messages'][-1].tool_calls
    tool_results = []
    for t in tool_calls:
        try:
            tool_func = finance_tool_map[t['name']]
            result = tool_func.invoke(t['args'])
        except KeyError:
            result = f"Error: Tool {t['name']} not found"
        except Exception as e:
            result = f"Error executing tool: {str(e)}"
        tool_results.append(ToolMessage(tool_call_id=t['id'], name=t['name'], content=str(result)))
    markdown_output = "\n\n"
    for result in tool_results:
        markdown_output += f"### {result.name.replace('_', ' ').title()}\n\n{result.content}\n\n"
    return {'messages': tool_results, 'running_summary': markdown_output}

def format_financial_analysis(state: State):
    messages = state['messages']
    tool_results = [msg for msg in messages if isinstance(msg, ToolMessage)]
    if tool_results:
        markdown_output = "\n\n"
        for result in tool_results:
            markdown_output += f"### {result.name.replace('_', ' ').title()}\n\n{result.content}\n\n"
    else:
        markdown_output = f"\n\n{messages[-1].content}"
    return {"running_summary": markdown_output, "messages": [HumanMessage(content=markdown_output)]}

# %%
# RAG Processing Functions
def process_rag_query(state: State):
    """Process RAG queries using BedrockRAGHandler"""
    try:
        # Initialize the RAG handler
        rag_handler = BedrockRAGHandler()

        # Get the user query
        query = state["research_topic"]

        # Query the knowledge base
        rag_response = rag_handler.query_knowledge_base(query)

        if rag_response.get("error"):
            error_message = f"RAG Error: {rag_response['error']}"
            return {
                "running_summary": error_message,
                "messages": [HumanMessage(content=error_message)],
                "original_messages": state["original_messages"]
            }

        # Format the response with sources
        answer = rag_response.get("answer", "No answer found.")
        sources = rag_response.get("sources", [])

        # Create formatted response
        formatted_response = f"## Knowledge Base Response\n\n{answer}"

        if sources:
            formatted_response += "\n\n### Sources:\n"
            for i, source in enumerate(sources, 1):
                formatted_response += f"{i}. {source}\n"

        return {
            "running_summary": formatted_response,
            "messages": [HumanMessage(content=formatted_response)],
            "original_messages": state["original_messages"]
        }

    except Exception as e:
        error_message = f"Error processing RAG query: {str(e)}"
        return {
            "running_summary": error_message,
            "messages": [HumanMessage(content=error_message)],
            "original_messages": state["original_messages"]
        }

# %%
def self_evaluate(input_text):
    parts = input_text.split("|||")
    query = parts[0]
    response = parts[1]
    sources = parts[2] if len(parts) > 2 else ""
    
    evaluation_prompt = f"""
    Evaluate the following response to the query:
    
    QUERY: {query}
    RESPONSE: {response}
    SOURCES: {sources}
    
    Assess based on:
    1. Factual accuracy (Does it match the sources?)
    2. Completeness (Does it address all aspects of the query?)
    3. Relevance (Is the information relevant to the query?)
    4. Hallucination (Does it contain information not supported by sources?)
    
    Return a confidence score from 0-10 and a brief explanation.
    """
    
    evaluation = llm.predict(evaluation_prompt)
    return evaluation

# %%
def evaluate_response(state: State, config: RunnableConfig):
    query = state.get("research_topic", "")
    response = state.get("running_summary", "")
    sources = "\n".join(state.get("sources_gathered", [])) or "No sources available"
    input_text = f"{query}|||{response}|||{sources}"
    evaluation = self_evaluate(input_text)
    final_summary = response# + "\n\n## Self Evaluation\n\n" + evaluation
    return {"running_summary": final_summary, "messages": [HumanMessage(content=final_summary)]}

def evaluation_decision(state: State, config: RunnableConfig):
    final_text = state.get("running_summary", "")
    prompt = f"""
    The final output and self-evaluation are as follows:
    {final_text}
    
    Based on the above, do you think additional insights should be added?
    If yes, return a JSON object with the key "next_route" set to one of the following options:
      - "call_llm" for additional financial analysis,
      - "web_research" for further web research,
      - "answer_normal_query" for more normal query insights,
    If no additional insights are needed, return "done".
    
    For example:
    {{"next_route": "call_llm"}}
    """
    result = llm.invoke(prompt)
    output_text = result.content.strip()
    try:
        decision = json.loads(output_text)
        next_route = decision.get("next_route", "done")
    except Exception as e:
        #print("Error in evaluation_decision:", e)
        next_route = "done"
    # Optionally update state with next_route
    state["next_route"] = next_route
    return {"next_route": next_route}

# %%
def get_route(state: State) -> str:
    return state["route"]

def call_route_first_step(state: State):
    image_processed = state.get("image_processed", False)
    if state.get("image") and len(state["image"]) > 0 and not image_processed:
        return {"route": "Image_Analysis", "original_messages": state["original_messages"]}
    
    router_response = llm.with_structured_output(Route_First_Step).invoke(state["research_topic"])
    print(f"Routing result: {router_response.step}")
    return {"route": router_response.step, "original_messages": state["original_messages"]}

def validate_state_transition(old_state: State, new_state: State):
    required_fields = set(State.__annotations__.keys())
    missing = required_fields - set(new_state.keys())
    if missing:
        raise ValueError(f"Missing state updates for: {missing}")
    return True

def after_image_analysis(state):
    return {**state}

# %%
def call_gemma3(state: State):
    try:
        image_path = state["image"][0]
        with open(image_path, "rb") as f:
            image_b64 = base64.b64encode(f.read()).decode()

        assert len(image_b64) < 180_000, \
            "To upload larger images, use the assets API (see docs)"
        
        try:
            invoke_url = "https://integrate.api.nvidia.com/v1/chat/completions"
            headers = {
                "Authorization": "Bearer **********************************************************************",
                "Accept": "application/json"
            }
            
            payload = {
                "model": "meta/llama-3.2-90b-vision-instruct",
                "messages": [
                    {
                        "role": "user",
                        "content": f"Describe what you see in this image. Focus on any charts, financial data, or technical analysis elements if present.\n<img src=\"data:image/png;base64,{image_b64}\" />"
                    }
                ],
                "max_tokens": 512,
                "temperature": 0.20,
                "top_p": 0.70,
                "stream": False
            }
            
            gemma_response = requests.post(invoke_url, headers=headers, json=payload)
            gemma_response.raise_for_status()
            data = gemma_response.json()
            full_response = data["choices"][0]["message"]["content"] if "choices" in data and data["choices"] else ""
        except Exception as e:
            fallback_prompt = f"Describe what you see in this image. The image appears to be a financial chart or technical analysis pattern graph from Investopedia. Please describe the patterns, trends, and elements visible in the chart."
            full_response = llm.invoke(fallback_prompt).content
        
        markdown_response = f"{full_response}"
        updated_messages = state["messages"] + [HumanMessage(content=markdown_response)]
        '''
        route_prompt = "Based on the image analysis of what appears to be a financial chart or technical analysis pattern, what should be the next step? Choose one: Web_query, Normal_query, Financial_Analysis"
        next_route = llm.invoke(route_prompt).content.strip()
        for route in ["Web_query", "Normal_query", "Financial_Analysis"]:
            if route in next_route:
                next_route = route
                break
        else:
            next_route = "Normal_query"
        '''
        return {
            "running_summary": markdown_response,
            "messages": updated_messages,
            "image_processed": True,
            "image": [],
            "original_messages": state["original_messages"]  # Preserve original messages
        }
    except Exception as e:
        return {
            "running_summary": str(e),
            "messages": state["messages"] + [HumanMessage(content=str(e))],
            "image_processed": True,
            "route": "Normal_query",
            "research_topic": state["research_topic"],
            "search_query": state.get("search_query", ""),
            "web_research_results": state.get("web_research_results", []),
            "sources_gathered": state.get("sources_gathered", []),
            "research_loop_count": state.get("research_loop_count", 0),
            "image": state["image"],
            "original_messages": state["original_messages"]  # Preserve original messages
        }

# %%
def process_with_context(state: State):
    """Node for processing queries with conversation context"""
    messages = state.get("messages", [])
    original_messages = state.get("original_messages", [])
    
    if len(messages) <= 1: 
        #print(messages, original_messages) # Only the current message, no context
        return {
            "messages": messages,
            "original_messages": original_messages,
            "research_topic": state["research_topic"]
        }
    
    # Last message is the current query
    current_query = messages[-1].content
    
    # Add the current query to original_messages if it's not already there
    if not original_messages or original_messages[-1].content != current_query:
        original_messages.append(HumanMessage(content=current_query))
    
    #print(original_messages)
    context_messages = messages[:-1]
    
    # Format the context for the prompt
    context_str = "\n".join([f"{'User' if i % 2 == 0 else 'Assistant'}: {msg.content}" 
                             for i, msg in enumerate(context_messages[-6:])])
    
    prompt = f"""
            Based on the previous conversation context and the user's current query, generate an enhanced version of the query that incorporates relevant context where appropriate. However, follow these specific rules based on the query type:

            Previous conversation:
            {context_str}

            Current query: {current_query}

            Instructions:
            1. **Plot-related queries**:
            - If the query mentions 'candlestick', 'balance sheet', 'assets', 'chart', 'visualize', or similar terms indicating a graph, identify the company ticker (e.g., AAPL, MSFT) and rephrase it into one of these exact structures with the ticker as the last word:
                - "Show me a candlestick chart for TICKER"
                - "Show me the balance sheet for TICKER"
                - "Show me the assets for TICKER"
            - Ignore the previous conversation context for these queries and focus solely on preserving the plotting intent.
            - If TICKER is not given but instead the company name, replace the company name with it's TICKER.
            - If any specific Indian company is given, add .NS at the end of the ticker name.
            - if NIFTY50 is asked, the TICKER IS '^NSEI', if NIFTY100 is asked the TICKER is '^CNX100', NIFTY MIDCAP 150 is asked the TICKER is 'NIFTYMIDCAP150.NS'.

            2. **Other queries**:
            - For all other queries (not related to plots), enhance the query by incorporating relevant details from the previous conversation context to make it more specific and contextually informed.
            - Avoid adding unnecessary complexity; only include context that directly relates to the current query.

            3. **General rules**:
            - If a ticker is present in a plot-related query, it must be the last word.
            - Maintain the user's original intent in all cases.
            - Keep the enhanced query natural and concise.

            Enhanced query:
            """
    
    try:
        enhanced_query = llm.invoke(prompt).content.strip()
        # Update the last message with the enhanced query in messages
        updated_messages = messages[:-1] + [HumanMessage(content=enhanced_query)]
        return {
            "messages": updated_messages,
            "original_messages": original_messages,
            "research_topic": enhanced_query
        }
    except Exception as e:
        # On error, return state with original messages preserved
        return {
            "messages": messages,
            "original_messages": original_messages,
            "research_topic": state["research_topic"]
        }

# %%
def update_router():
    final_router = StateGraph(State)
    
    # Add all nodes
    final_router.add_node("route_first_step", call_route_first_step)
    final_router.add_node("generate_query", generate_query)
    final_router.add_node("web_research", web_research)
    final_router.add_node("summarize_sources", summarize_sources)
    final_router.add_node("reflect_on_summary", reflect_on_summary)
    final_router.add_node("finalize_summary", finalize_summary)
    final_router.add_node('call_llm', call_llm)
    final_router.add_node('take_action', take_action)
    final_router.add_node('format_financial_analysis', format_financial_analysis)
    final_router.add_node('answer_normal_query', answer_normal_query)
    final_router.add_node("self_evaluate_final", evaluate_response)
    final_router.add_node("evaluation_decision", evaluation_decision)
    final_router.add_node("process_with_context", process_with_context)
    # Add the new image processing node
    final_router.add_node("image_analysis", call_gemma3)

    final_router.add_node("parse_query", parse_query)
    final_router.add_node("generate_plot", generate_plot)
    final_router.add_node("format_response", format_response)
    # Add RAG node
    final_router.add_node("process_rag_query", process_rag_query)

    # Define connections
    final_router.add_edge(START, "process_with_context")
    final_router.add_edge("process_with_context", "route_first_step")

    # Update conditional edges to include Image_Analysis and RAG routes
    final_router.add_conditional_edges("route_first_step", get_route, {
        'Image_Analysis': 'image_analysis',
        'Web_query': 'generate_query',
        'Normal_query': 'answer_normal_query',
        'Financial_Analysis': 'call_llm',
        'Plot_Graph': 'parse_query',
        'RAG_query': 'process_rag_query'
    })
    
    # Add edge from image_analysis to subsequent routing
    final_router.add_edge("parse_query", "generate_plot")
    final_router.add_edge("generate_plot", "format_response")
    final_router.add_edge("image_analysis",END)
    
    final_router.add_edge("answer_normal_query", 'self_evaluate_final')
    final_router.add_edge("format_response", 'self_evaluate_final')
    final_router.add_edge("process_rag_query", 'self_evaluate_final')
    
    final_router.add_conditional_edges(
        "call_llm",
        exists_action,
        {True: "take_action", False: "format_financial_analysis"}
    )
    final_router.add_edge("take_action", "format_financial_analysis")
    final_router.add_edge("format_financial_analysis", END)
    
    final_router.add_edge("generate_query", "web_research")
    final_router.add_edge("web_research", "summarize_sources")
    final_router.add_edge("summarize_sources", "reflect_on_summary")
    final_router.add_conditional_edges("reflect_on_summary", route_research)
    final_router.add_edge("finalize_summary", 'self_evaluate_final')
    final_router.add_edge("self_evaluate_final", 'evaluation_decision')
    
    final_router.add_conditional_edges("evaluation_decision", lambda x: x.get("next_route", "done"), {
        'done': END,
        'call_llm': 'call_llm',
        'web_research': 'web_research',
        'answer_normal_query': 'answer_normal_query'
    })
    
    return final_router.compile()

# %%
class FinancialChatBot:
    def __init__(self, language = 'english'):
        self.conversation_history = []
        self.model = update_router()
        self.context_messages = []
        self.language = language  # Store actual message objects for context
        
    def _format_bot_message(self, content: str) -> str:
        """Format the bot's message for display"""
        return f"🤖 Assistant: {content}"
    
    def _format_user_message(self, content: str) -> str:
        """Format the user's message for display"""
        return f"👤 User: {content}"
    
    def _update_context(self, user_input: str, bot_response: str):
        """Update the context messages for the next interaction"""
              
        # Add to context messages (for model processing)
        self.context_messages.append(HumanMessage(content=user_input))
        self.context_messages.append(AIMessage(content=bot_response))
        
        # Keep context within a reasonable size (last 5 interactions = 10 messages)
        if len(self.context_messages) > 10:
            self.context_messages = self.context_messages[-10:]
    
    def _process_with_context(self, user_input: str):
        """Generate a contextualized query based on conversation history"""
        
        if not self.context_messages:
            return user_input
        
        # Create a prompt to contextualize the query
        context_system_prompt = """
        You are a market research analyst analyzing a conversation history.
        Given the conversation history and a new user query, your task is to:
        1. Understand the context of the ongoing conversation
        2. Generate an enhanced version of the user's query that incorporates relevant context
        3. Return ONLY the enhanced query without any explanations
        """
        
        # Create a formatted context
        context_prompt = "Conversation history:\n"
        for msg in self.context_messages[-6:]:  # Use last 3 interactions max
            role = "User" if isinstance(msg, HumanMessage) else "Assistant"
            context_prompt += f"{role}: {msg.content}\n\n"
        
        context_prompt += f"New user query: {user_input}\n\nGenerate an enhanced query that incorporates context:"
        
        # Use LLM to generate contextualized query
        try:
            messages = [
                SystemMessage(content=context_system_prompt),
                HumanMessage(content=context_prompt)
            ]
            enhanced_query = llm.invoke(messages).content.strip()
            return enhanced_query
        except Exception as e:
            #print(f"Context processing error: {e}")
            return user_input 
        
    def chat(self, user_input: str, image_path: str = None) -> dict:
        """
        Process a single chat interaction with context awareness
        
        Args:
            user_input (str): The user's message
            image_path (str, optional): Path to an image if one is provided
            
        Returns:
            dict: Dictionary with 'text' (response text) and 'plot' (plot JSON or None)
        """
        # Add user message to display history
        self.conversation_history.append(self._format_user_message(user_input))
        
        # Skip contextualizing if this is the first message or providing an image
        contextualized_input = user_input
        if self.context_messages and not image_path:
            contextualized_input = self._process_with_context(user_input)
        
        # Create initial state with image if provided
        image_list = [image_path] if image_path else []
        initial_state = create_initial_state(contextualized_input, image_list)
        
        # Add all previous messages to the state
        if self.context_messages:
            initial_state["messages"] = self.context_messages + [HumanMessage(content=contextualized_input)]
        
        try:
            # Process through the model
            response = self.model.invoke(initial_state)
            print(response)
            # Extract text response and plot data
            text_response = response.get('running_summary', '')
            plot_json = response.get('plot_json')
            
            if not text_response and response.get('messages'):
                # Fallback to last message content if running_summary is empty
                text_response = response['messages'][-1].content
            
            url = 'https://api.two.ai/v2';

            client = OpenAI(base_url=url,
                            api_key=os.getenv('SUTRA_API_KEY'))
            self._update_context(user_input, text_response)
            formatted_response = self._format_bot_message(text_response)
            self.conversation_history.append(formatted_response)
            print(self.language)
            print(type(self.language), type(text_response))
            if self.language != "english":
                print('In translation part:')
                stream = client.chat.completions.create(model='sutra-v2',
                                                    messages = [{"role": "user", "content": "Translate this text in" + self.language + ": " + text_response}],
                                                    max_tokens=1024,
                                                    temperature=0,
                                                    stream=False)  
                print(stream.choices[0].message.content)
                return {"text": stream.choices[0].message.content, "plot": plot_json}
            
            return {"text": text_response, "plot": plot_json}
        
        except Exception as e:
            error_message = f"I apologize, but I encountered an error: {str(e)}"
            self.conversation_history.append(self._format_bot_message(error_message))
            return {"text": error_message, "plot": None}
    
    def get_conversation_history(self) -> str:
        """Return the full conversation history"""
        return "\n\n".join(self.conversation_history)
    
    def clear_history(self):
        """Clear the conversation history"""
        self.conversation_history = []
        self.context_messages = []

# %%
def main():
    # Initialize the chatbot
    chatbot = FinancialChatBot()
        
    url = 'https://api.two.ai/v2'

    client = OpenAI(base_url=url,
                    api_key=os.environ.get("SUTRA_API_KEY"))
    
    #print("Welcome to the Financial Assistant! (Type 'quit' to exit)")
    #print("You can also share images by typing 'image: ' followed by the image path")
    
    while True:
        user_input = input("\n👤 You: ").strip()
        
        if user_input.lower() == 'quit':
            print("\nGoodbye! Thank you for using the Financial Assistant.")
            break
            
        # Check if user is sharing an image
        image_path = None
        if user_input.startswith('image:'):
            image_path = user_input[6:].strip()
            user_input = "What do you see in this image?"
        
        # Get bot's response
        response = chatbot.chat(user_input, image_path)
        print(response)

        if chatbot.language != 'english':
            stream = client.chat.completions.create(model='sutra-v2',
                                                    messages = [{"role": "user", "content": "Translate this text in" + chatbot.language + ": " + response}],
                                                    max_tokens=1024,
                                                    temperature=0,
                                                    stream=False)

            print("\n🤖 Assistant:\n",)
            for chunk in stream:
                if len(chunk.choices) > 0:
                    content = chunk.choices[0].delta.content
                    finish_reason = chunk.choices[0].finish_reason
                    if content and finish_reason is None:
                        print(content, end='', flush=True)

# %%
if __name__ == "__main__":
    main()