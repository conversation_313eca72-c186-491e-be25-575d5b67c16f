{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9f2ff75c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\genai_project_env\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Singapore Economic Insight Generator ===\n", "\n", "\n", "--- Fetching economic data for SG ---\n", "Fetched GDP Growth data (top 5):\n", "        date     value\n", "0 2024-01-01  4.388024\n", "1 2023-01-01  1.821407\n", "2 2022-01-01  4.108000\n", "3 2021-01-01  9.756804\n", "4 2020-01-01 -3.814709\n", "Fetched Inflation data (top 5):\n", "        date     value\n", "0 2024-01-01  2.389511\n", "1 2023-01-01  4.833724\n", "2 2022-01-01  6.130954\n", "3 2021-01-01  2.316103\n", "4 2020-01-01 -0.172112\n", "\n", "--- Formatting data for LLM for SG ---\n", "Formatted GDP Growth snippet for LLM:\n", " | date                |    value |\n", "|:--------------------|---------:|\n", "| 2024-01-01 00:00:00 |  4.38802 |\n", "| 2023-01-01 00:00:00 |  1.82141 |\n", "| 2022-01-01 00:00:00 |  4.108   |\n", "| 2021-01-01 00:00:00 |  9.7568  |...\n", "Formatted Inflation snippet for LLM:\n", " | date                |     value |\n", "|:--------------------|----------:|\n", "| 2024-01-01 00:00:00 |  2.38951  |\n", "| 2023-01-01 00:00:00 |  4.83372  |\n", "| 2022-01-01 00:00:00 |  6.13095  |\n", "| 2021-01-01 00:00:00 |  2.3161   |...\n", "\n", "--- Prepared Prompt for LLM (truncated) ---\n", "You are an expert economic analyst. Your task is to analyze the provided Singaporean economic data and offer concise, actionable insights, identifying key trends, year-over-year changes, and potential implications. Focus on GDP growth and inflation figures for Singapore.\n", "\n", "--- Singapore GDP Growth (Annual %) ---\n", "| date                |    value |\n", "|:--------------------|---------:|\n", "| 2024-01-01 00:00:00 |  4.38802 |\n", "| 2023-01-01 00:00:00 |  1.82141 |\n", "| 2022-01-01 00:00:00 |  4.108   |\n", "| 2021-01-01 00:00:00 |  9.7568  |\n", "| 2020-01-01 00:00:00 | -3.81471 |\n", "| 2019-01-01 00:00:00 |  1.30804 |\n", "| 2018-01-01 00:00:00 |  3.45198 |\n", "| 2017-01-01 00:00:00 |  4.4769  |\n", "| 2016-01-01 00:00:00 |  3.74758 |\n", "| 2015-01-01 00:00:00 |  2.9768  |\n", "| 2014-01-01 00:00:00 |  3.93554 |\n", "| 2013-01-01 00:00:00 |  4.81763...\n", "\n", "--- Calling Groq LLM for Insights ---\n", "\n", "========================================\n", "=== Final Economic Insights for Singapore ===\n", "========================================\n", "### Economic Insights: Singapore's GDP Growth and Inflation Trends\n", "\n", "#### 1. Key Trends in GDP Growth and Inflation\n", "\n", "*   **GDP Growth:**\n", "    *   Experienced significant fluctuations over the years, with a notable peak in 2010 (14.52%) followed by a decline and recovery.\n", "    *   Showed resilience with positive growth in most years, except for 2020 (-3.81%) due to the COVID-19 pandemic.\n", "    *   Recent years (2021-2024) indicate a recovery phase, with 2021 seeing a strong rebound (9.76%) and 2024 starting with a growth rate of 4.39%.\n", "*   **Inflation:**\n", "    *   Generally remained under control, with periods of low or negative inflation rates, particularly from 2015 to 2020.\n", "    *   Experienced a rise in inflation from 2021 to 2022, peaking at 6.13% in 2022, likely due to global supply chain disruptions and economic recovery post-pandemic.\n", "    *   Inflation has been on a downward trend since 2022, with a rate of 2.39% in 2024, indicating a return to more stable price levels.\n", "\n", "#### 2. Notable Positive or Negative Changes and Specific Years\n", "\n", "*   **Notable Changes:**\n", "    *   **2020:** GDP contracted by 3.81% due to the COVID-19 pandemic, marking a significant negative impact.\n", "    *   **2021:** GDP rebounded strongly with a 9.76% growth rate, indicating a swift recovery.\n", "    *   **2022:** Inflation peaked at 6.13%, highlighting the impact of global economic factors on domestic prices.\n", "    *   **2024:** GDP growth started the year at 4.39%, and inflation was at 2.39%, suggesting a balanced economic recovery.\n", "\n", "#### 3. Apparent Correlations Between GDP and Inflation\n", "\n", "*   The data suggests that periods of high GDP growth are sometimes associated with rising inflation, as seen in the recovery phase post-2020.\n", "*   For instance, the strong GDP growth in 2010 (14.52%) was accompanied by a relatively moderate inflation rate (2.83%), indicating that high growth does not always lead to high inflation.\n", "*   However, the surge in GDP growth in 2021 (9.76%) was followed by rising inflation in 2022 (6.13%), suggesting that rapid economic expansion can lead to increased price pressures.\n", "\n", "#### 4. Overall Assessment and Future Outlook\n", "\n", "*   **Recent Performance:** Singapore's economy has shown resilience and the ability to recover from the pandemic-induced downturn. The GDP growth has been positive, and inflation, while rising in 2022, has returned to more manageable levels.\n", "*   **Future Outlook:** \n", "    *   The current GDP growth rate and controlled inflation suggest a stable economic environment.\n", "    *   Continued global economic stability and the absence of major supply chain disruptions will be crucial for maintaining this trajectory.\n", "    *   Policymakers will need to monitor inflation closely and adjust monetary policies as necessary to ensure that economic growth remains sustainable.\n", "\n", "Overall, Singapore's economic data indicates a robust recovery and a relatively stable economic environment entering 2024, with both GDP growth and inflation under control.\n", "========================================\n"]}], "source": ["import os\n", "import requests\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "import json\n", "import time\n", "from typing import Dict, List, Optional, Any, TypedDict, Annotated\n", "from dotenv import load_dotenv\n", "import operator\n", "\n", "# Langchain and Groq imports\n", "from groq import Groq\n", "from langchain_groq import ChatGroq\n", "from langgraph.graph import StateGraph, END\n", "\n", "# --- Start of your FreeEconomicDataAPI Class (Copied verbatim) ---\n", "class FreeEconomicDataAPI:\n", "    \"\"\"\n", "    Comprehensive API client for free economic data sources\n", "    Combines World Bank, IMF, FRED, OECD, and other free sources\n", "    \"\"\"\n", "    \n", "    def __init__(self, fred_api_key: Optional[str] = None):\n", "        \"\"\"\n", "        Initialize the API client\n", "        \n", "        Args:\n", "            fred_api_key: Optional FRED API key (free from https://fred.stlouisfed.org/docs/api/)\n", "        \"\"\"\n", "        self.fred_api_key = fred_api_key\n", "        self.session = requests.Session()\n", "        self.session.headers.update({\n", "            'User-Agent': 'Economic-Data-Client/1.0'\n", "        })\n", "    \n", "    # ===========================================\n", "    # WORLD BANK API (Primary Source)\n", "    # ===========================================\n", "    \n", "    def get_world_bank_countries(self) -> List[Dict]:\n", "        \"\"\"Get list of all countries from World Bank\"\"\"\n", "        url = \"https://api.worldbank.org/v2/countries\"\n", "        params = {'format': 'json', 'per_page': 500}\n", "        \n", "        try:\n", "            response = self.session.get(url, params=params)\n", "            response.raise_for_status()\n", "            data = response.json()\n", "            return data[1] if len(data) > 1 else []\n", "        except Exception as e:\n", "            print(f\"Error fetching World Bank countries: {e}\")\n", "            return []\n", "    \n", "    def get_world_bank_indicators(self) -> List[Dict]:\n", "        \"\"\"Get list of all available indicators from World Bank\"\"\"\n", "        url = \"https://api.worldbank.org/v2/indicators\"\n", "        params = {'format': 'json', 'per_page': 1000}\n", "        \n", "        try:\n", "            response = self.session.get(url, params=params)\n", "            response.raise_for_status()\n", "            data = response.json()\n", "            return data[1] if len(data) > 1 else []\n", "        except Exception as e:\n", "            print(f\"Error fetching World Bank indicators: {e}\")\n", "            return []\n", "    \n", "    def get_world_bank_data(self, country_code: str, indicator: str, \n", "                           start_year: str = \"2010\", end_year: str = \"2024\") -> Dict:\n", "        \"\"\"\n", "        Get World Bank economic data\n", "        \n", "        Args:\n", "            country_code: ISO2 code (SG, US, GB) or 'all' for all countries\n", "            indicator: World Bank indicator code\n", "            start_year: Start year (YYYY)\n", "            end_year: End year (YYYY)\n", "        \"\"\"\n", "        url = f\"https://api.worldbank.org/v2/countries/{country_code}/indicators/{indicator}\"\n", "        params = {\n", "            'format': 'json',\n", "            'date': f\"{start_year}:{end_year}\",\n", "            'per_page': 1000\n", "        }\n", "        \n", "        try:\n", "            response = self.session.get(url, params=params)\n", "            response.raise_for_status()\n", "            data = response.json()\n", "            return {\n", "                'source': 'World Bank',\n", "                'country': country_code,\n", "                'indicator': indicator,\n", "                'data': data[1] if len(data) > 1 else []\n", "            }\n", "        except Exception as e:\n", "            print(f\"Error fetching World Bank data for {country_code} ({indicator}): {e}\")\n", "            return {'source': 'World Bank', 'error': str(e)}\n", "    \n", "    # ===========================================\n", "    # FRED API (US Federal Reserve)\n", "    # ===========================================\n", "    \n", "    def get_fred_data(self, series_id: str, start_date: str = None, end_date: str = None) -> Dict:\n", "        \"\"\"Get FRED economic data\"\"\"\n", "        if not self.fred_api_key:\n", "            return {'source': 'FRED', 'error': 'FRED API key required'}\n", "        \n", "        url = \"https://api.stlouisfed.org/fred/series/observations\"\n", "        params = {\n", "            'series_id': series_id,\n", "            'api_key': self.fred_api_key,\n", "            'file_type': 'json'\n", "        }\n", "        if start_date: params['observation_start'] = start_date\n", "        if end_date: params['observation_end'] = end_date\n", "        \n", "        try:\n", "            response = self.session.get(url, params=params)\n", "            response.raise_for_status()\n", "            return {'source': 'FRED', 'series_id': series_id, 'data': response.json().get('observations', [])}\n", "        except Exception as e:\n", "            print(f\"Error fetching FRED data: {e}\")\n", "            return {'source': 'FRED', 'error': str(e)}\n", "    \n", "    # ===========================================\n", "    # COMBINED METHODS\n", "    # ===========================================\n", "    \n", "    def get_country_overview(self, country_code: str) -> Dict:\n", "        \"\"\"\n", "        Get comprehensive country data from available free sources\n", "        \n", "        Args:\n", "            country_code: ISO2 country code (e.g., 'SG', 'US', 'GB')\n", "        \"\"\"\n", "        overview = {\n", "            'country': country_code,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'sources': {}\n", "        }\n", "        \n", "        # World Bank - Key Economic Indicators\n", "        wb_indicators = {\n", "            'GDP': 'NY.GDP.MKTP.CD',           # GDP (current US$)\n", "            'GDP_Growth': 'NY.GDP.MKTP.KD.ZG', # GDP growth (annual %)\n", "            'Population': 'SP.POP.TOTL',        # Total population\n", "            'Unemployment': 'SL.UEM.TOTL.ZS',   # Unemployment (% of total labor force)\n", "            'Inflation': 'FP.CPI.TOTL.ZG',      # Inflation, consumer prices (annual %)\n", "            'Trade_Balance': 'BN.CAB.XOKA.CD'   # Current account balance (BoP, current US$)\n", "        }\n", "        \n", "        wb_data = {}\n", "        for name, indicator in wb_indicators.items():\n", "            data = self.get_world_bank_data(country_code, indicator)\n", "            wb_data[name] = data\n", "            time.sleep(0.1)  # Be respectful to API\n", "        \n", "        overview['sources']['world_bank'] = wb_data\n", "        \n", "        # FRED data (only runs for US and if API key is available)\n", "        if country_code.upper() == 'US' and self.fred_api_key:\n", "            fred_series = {\n", "                'GDP': 'GDP',\n", "                'Unemployment_Rate': 'UNRATE',\n", "                'CPI': 'CPIAUCSL',\n", "                'Federal_Funds_Rate': 'FEDFUNDS'\n", "            }\n", "            fred_data = {}\n", "            for name, series_id in fred_series.items():\n", "                data = self.get_fred_data(series_id)\n", "                fred_data[name] = data\n", "                time.sleep(0.1)\n", "            overview['sources']['fred'] = fred_data\n", "        \n", "        return overview\n", "    \n", "    def get_global_comparison(self, indicator: str, countries: List[str]) -> Dict:\n", "        \"\"\"Compare specific indicator across multiple countries\"\"\"\n", "        comparison = {'indicator': indicator, 'countries': {}, 'timestamp': datetime.now().isoformat()}\n", "        for country in countries:\n", "            data = self.get_world_bank_data(country, indicator)\n", "            comparison['countries'][country] = data\n", "            time.sleep(0.1)\n", "        return comparison\n", "    \n", "    def to_dataframe(self, data: Dict) -> pd.DataFrame:\n", "        \"\"\"Convert World Bank or FRED API response to pandas DataFrame\"\"\"\n", "        try:\n", "            source = data.get('source', '').lower()\n", "            if source == 'world bank' or source == 'fred':\n", "                df = pd.DataFrame(data.get('data', []))\n", "                if not df.empty and 'date' in df.columns:\n", "                    df['date'] = pd.to_datetime(df['date'])\n", "                    # FRED data has '.' for missing values, convert to numeric\n", "                    if 'value' in df.columns:\n", "                        df['value'] = pd.to_numeric(df['value'], errors='coerce')\n", "                    df = df.sort_values('date', ascending=False)\n", "                return df\n", "            return pd.DataFrame(data)\n", "        except Exception as e:\n", "            print(f\"Error converting to DataFrame: {e}\")\n", "            return pd.DataFrame()\n", "# --- End of your FreeEconomicDataAPI Class ---\n", "\n", "\n", "# --- Langgraph Setup for Economic Insights ---\n", "\n", "# Define the state for our graph.\n", "class EconomicAgentState(TypedDict):\n", "    \"\"\"Represents the state of our economic analysis workflow.\"\"\"\n", "    api_client: FreeEconomicDataAPI  # The API client instance\n", "    country_code: str\n", "    gdp_growth_df: Optional[pd.DataFrame]\n", "    inflation_df: Optional[pd.DataFrame]\n", "    gdp_growth_str: str  # String representation for LLM\n", "    inflation_str: str   # String representation for LLM\n", "    analysis_prompt: str\n", "    llm_insights: str\n", "\n", "# Node 1: Fetch economic data using the FreeEconomicDataAPI\n", "def fetch_economic_data(state: EconomicAgentState) -> Dict:\n", "    \"\"\"\n", "    Fetches GDP growth and inflation data for the specified country.\n", "    \"\"\"\n", "    api_client = state[\"api_client\"]\n", "    country_code = state[\"country_code\"]\n", "    print(f\"\\n--- Fetching economic data for {country_code} ---\")\n", "\n", "    gdp_data_raw = api_client.get_world_bank_data(country_code, 'NY.GDP.MKTP.KD.ZG', start_year=\"2010\", end_year=\"2024\")\n", "    inflation_data_raw = api_client.get_world_bank_data(country_code, 'FP.CPI.TOTL.ZG', start_year=\"2010\", end_year=\"2024\")\n", "\n", "    gdp_df = api_client.to_dataframe(gdp_data_raw).dropna(subset=['value'])# Get top 15 non-null values\n", "    inflation_df = api_client.to_dataframe(inflation_data_raw).dropna(subset=['value'])# Get top 15 non-null values\n", "\n", "    print(f\"Fetched GDP Growth data (top 5):\\n{gdp_df[['date', 'value']].head()}\")\n", "    print(f\"Fetched Inflation data (top 5):\\n{inflation_df[['date', 'value']].head()}\")\n", "\n", "    return {\n", "        \"gdp_growth_df\": gdp_df,\n", "        \"inflation_df\": inflation_df\n", "    }\n", "\n", "# Node 2: Format data into a string for the LLM\n", "def format_data_for_llm(state: EconomicAgentState) -> Dict:\n", "    \"\"\"\n", "    Converts DataFrames into string representations suitable for the LLM.\n", "    \"\"\"\n", "    gdp_df = state[\"gdp_growth_df\"]\n", "    inflation_df = state[\"inflation_df\"]\n", "    country_code = state[\"country_code\"]\n", "    print(f\"\\n--- Formatting data for LLM for {country_code} ---\")\n", "\n", "    gdp_str = \"\"\n", "    if not gdp_df.empty:\n", "        gdp_str = gdp_df[['date', 'value']].to_markdown(index=False)\n", "    else:\n", "        gdp_str = \"No GDP Growth data available.\"\n", "\n", "    inflation_str = \"\"\n", "    if not inflation_df.empty:\n", "        inflation_str = inflation_df[['date', 'value']].to_markdown(index=False)\n", "    else:\n", "        inflation_str = \"No Inflation data available.\"\n", "\n", "    print(\"Formatted GDP Growth snippet for LLM:\\n\", gdp_str.split('\\n')[0] + '\\n' + '\\n'.join(gdp_str.split('\\n')[1:6]) + '...' if len(gdp_str.split('\\n')) > 6 else gdp_str)\n", "    print(\"Formatted Inflation snippet for LLM:\\n\", inflation_str.split('\\n')[0] + '\\n' + '\\n'.join(inflation_str.split('\\n')[1:6]) + '...' if len(inflation_str.split('\\n')) > 6 else inflation_str)\n", "\n", "    return {\n", "        \"gdp_growth_str\": gdp_str,\n", "        \"inflation_str\": inflation_str\n", "    }\n", "\n", "# Node 3: Prepare the analysis prompt for the LLM\n", "def prepare_analysis_prompt(state: EconomicAgentState) -> dict:\n", "    \"\"\"\n", "    Formats the economic data strings into a structured prompt for the LLM.\n", "    \"\"\"\n", "    gdp_data = state[\"gdp_growth_str\"]\n", "    inflation_data = state[\"inflation_str\"]\n", "    country_code = state[\"country_code\"]\n", "    \n", "    prompt = (\n", "        f\"You are an expert economic analyst. Your task is to analyze the provided \"\n", "        f\"Singaporean economic data and offer concise, actionable insights, \"\n", "        \"identifying key trends, year-over-year changes, and potential implications. \"\n", "        \"Focus on GDP growth and inflation figures for Singapore.\\n\\n\"\n", "        \"--- Singapore GDP Growth (Annual %) ---\\n\"\n", "        f\"{gdp_data}\\n\\n\"\n", "        \"--- Singapore Inflation (Annual %) ---\\n\"\n", "        f\"{inflation_data}\\n\\n\"\n", "        \"Please provide your economic insights, highlighting: \\n\"\n", "        \"1. Key trends in GDP growth and inflation over the period (e.g., periods of high/low growth, rising/falling inflation).\\n\"\n", "        \"2. Notable positive or negative changes and specific years.\\n\"\n", "        \"3. Any apparent correlations between GDP and inflation (e.g., high growth with rising inflation).\\n\"\n", "        \"4. A brief overall assessment of Singapore's recent economic performance based on this data, mentioning potential future outlook.\\n\"\n", "        \"Your response should be structured with clear headings and bullet points, and easy to understand.\"\n", "    )\n", "    print(\"\\n--- Prepared Prompt for LLM (truncated) ---\")\n", "    print(prompt[:800] + \"...\" if len(prompt) > 800 else prompt) # Print a truncated prompt for brevity\n", "    return {\"analysis_prompt\": prompt}\n", "\n", "# Node 4: Call the LLM (Groq) to get insights\n", "def get_llm_economic_insights(state: EconomicAgentState) -> dict:\n", "    \"\"\"\n", "    Calls the Groq LLM with the prepared prompt and returns its analysis.\n", "    \"\"\"\n", "    # Initialize the ChatGroq model\n", "    # You can choose different models like \"llama3-8b-8192\", \"llama3-70b-8192\", or \"mixtral-8x7b-32768\"\n", "    # Adjust 'temperature' for creativity (0.0 for more factual, higher for more creative)\n", "    llm = ChatGroq(model=\"meta-llama/llama-4-maverick-17b-128e-instruct\", temperature=0.0)\n", "\n", "    print(\"\\n--- Calling Groq LLM for Insights ---\")\n", "    try:\n", "        response = llm.invoke(state[\"analysis_prompt\"])\n", "        return {\"llm_insights\": response.content}\n", "    except Exception as e:\n", "        print(f\"Error calling Groq API: {e}\")\n", "        print(\"Please ensure your GROQ_API_KEY is correctly set and you have network access.\")\n", "        return {\"llm_insights\": f\"Error: Could not retrieve insights due to API call failure: {e}\"}\n", "\n", "# Build the Langgraph workflow\n", "workflow = StateGraph(EconomicAgentState)\n", "\n", "# Add nodes to the workflow\n", "workflow.add_node(\"fetch_data\", fetch_economic_data)\n", "workflow.add_node(\"format_data\", format_data_for_llm)\n", "workflow.add_node(\"prepare_prompt\", prepare_analysis_prompt)\n", "workflow.add_node(\"get_insights\", get_llm_economic_insights)\n", "\n", "# Set the entry point for the graph\n", "workflow.set_entry_point(\"fetch_data\")\n", "\n", "# Define the edges (transitions between nodes)\n", "workflow.add_edge(\"fetch_data\", \"format_data\")\n", "workflow.add_edge(\"format_data\", \"prepare_prompt\")\n", "workflow.add_edge(\"prepare_prompt\", \"get_insights\")\n", "workflow.add_edge(\"get_insights\", END) # The graph ends after getting insights\n", "\n", "# Compile the graph into an executable application\n", "app = workflow.compile()\n", "\n", "# --- Main execution ---\n", "if __name__ == \"__main__\":\n", "    load_dotenv() # Load environment variables from .env file\n", "\n", "    # --- 1. Set your Groq API Key ---\n", "    # It's recommended to set your API key as an environment variable.\n", "    # Example: GROQ_API_KEY=\"your_groq_api_key_here\" in a .env file\n", "    if \"GROQ_API_KEY\" not in os.environ:\n", "        print(\"WARNING: GROQ_API_KEY environment variable not set.\")\n", "        print(\"Please set it before running the script (e.g., in a .env file or export GROQ_API_KEY='your_key')\")\n", "        print(\"You can get a key from: https://console.groq.com/keys\")\n", "        exit(\"Exiting: GROQ_API_KEY is required for the LLM to function.\")\n", "\n", "    print(\"=== Singapore Economic Insight Generator ===\\n\")\n", "\n", "    # Initialize your FreeEconomicDataAPI\n", "    fred_api_key = os.getenv(\"FRED_API_KEY\") # FRED key is optional for World Bank data\n", "    economic_api_client = FreeEconomicDataAPI(fred_api_key=fred_api_key)\n", "\n", "    # Initial state for the Langgraph workflow\n", "    initial_state = {\n", "        \"api_client\": economic_api_client,\n", "        \"country_code\": \"SG\", # Focusing on Singapore\n", "        \"gdp_growth_df\": None,\n", "        \"inflation_df\": None,\n", "        \"gdp_growth_str\": \"\",\n", "        \"inflation_str\": \"\",\n", "        \"analysis_prompt\": \"\",\n", "        \"llm_insights\": \"\"\n", "    }\n", "\n", "    try:\n", "        # Run the Langgraph workflow\n", "        final_state = app.invoke(initial_state)\n", "\n", "        print(\"\\n\" + \"=\"*40)\n", "        print(\"=== Final Economic Insights for Singapore ===\")\n", "        print(\"=\"*40)\n", "        print(final_state[\"llm_insights\"])\n", "        print(\"=\"*40)\n", "\n", "    except Exception as e:\n", "        print(f\"\\nAn error occurred during workflow execution: {e}\")\n", "        print(\"Please check your Groq API key, network connection, and API limits.\")"]}], "metadata": {"kernelspec": {"display_name": "genai_project_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}