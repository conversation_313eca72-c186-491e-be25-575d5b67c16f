# FinFriend - Your AI-Powered Financial Assistant

## 📌 Overview
FinFriend is an AI-powered financial assistant designed to enhance financial literacy and improve investment decisions among users. By integrating various tools and resources, FinFriend simplifies complex financial concepts, making them accessible and actionable for users at all levels of financial expertise.

## 📖 Table of Contents
1. [Overview](#-overview)
2. [Problem Resolution](#-problem-resolution)
3. [How Does FinFriend Solve These Problems?](#-how-does-finfriend-solve-these-problems)
   - [Interactive Learning](#-interactive-learning)
   - [Personalized Recommendations](#-personalized-recommendations)
   - [Real-Time Information](#-real-time-information)
4. [Unique Value Propositions (UVP)](#-unique-value-propositions-uvp)
5. [Tech Stack](#-tech-stack)
6. [Getting Started](#-getting-started)
   - [Prerequisites](#-prerequisites)
   - [Installation](#-installation)
7. [Demo Video](#demo-video)
8. [Contact](#-contact)

## 🚀 Problem Resolution
FinFriend addresses key challenges in financial literacy and investing:
- **Bridging the financial literacy gap** with accessible investment knowledge.
- **AI-driven guidance** for scalable and personalized financial advice.
- **Simplifying investing** for both beginners and experienced users.
- **Detecting fraud risks** to protect investors from scams.
- **Empowering users** with educational resources and market updates.

## 💡 How Does FinFriend Solve These Problems?
### 🔹 Interactive Learning
Engages users with **quizzes, lessons, and a finance dictionary**, making financial education engaging and easy to understand.

### 🔹 Personalized Recommendations
Offers **tailored investment and financial product suggestions** based on user profiles, risk appetite, and market conditions.

### 🔹 Real-Time Information
Provides **up-to-date financial news, stock movements, and market trends**, helping users make informed investment decisions.


## ✨ Unique Value Propositions (UVP)
- **AI-powered financial assistant** providing real-time and intuitive guidance.
- **Instant insights** tailored to individual user needs.
- **24/7 availability** without human intervention.
- **Secure & data-driven** with advanced market analysis.

## ⚙️ Tech Stack
- Python – Primary programming language
- Streamlit – for fast prototyping and interactive UI and to handle app deployment
- Scikit-learn for ML-based stock analysis
- Prophet for time series forecasting and price prediction
- Integrated with Gemini, Groq, Tavily, LangChain
- Uses LLaMA 3.3, Gemma for advanced reasoning
- YouTube API for content suggestions
- Yahoo Finance API for stock data extraction
- GitHub – For version control and collaboration
- Firebase Authentication for secure user login
- Firebase Firestore to store:User profiles, Forum discussions, Auth metadata
- Gemini – Integrated for AI-powered insights
- NVIDIA NIM - for image analysis models



## 📖 Getting Started
### Prerequisites
Ensure you have the following installed:
- Python 3.12+
- Streamlit
- Firebase CLI (if using Firebase for authentication & database)

### Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/gdsc.git
   cd gdsc
   ```
2. Install dependencies:
   ```bash
   pip install -r requirements.txt  # Install all the dependencies
   ```
3. Run the app:
   ```bash
   streamlit run app.py
   ```

## Demo Video
https://youtu.be/wYLyWq4Gubc

## 📬 Contact
For any inquiries, reach out via:
- <EMAIL>
- <EMAIL>
- <EMAIL>


