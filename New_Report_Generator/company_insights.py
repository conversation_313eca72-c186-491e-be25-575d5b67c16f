# company_insights.py

import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from tavily import TavilyClient
import google.generativeai as genai
import datetime
from dotenv import load_dotenv

load_dotenv() # Load environment variables here as well, in case this file is run standalone or for robust access.

# --- Configuration & Blocklists ---
# DOMAIN_BLOCKLIST is commented out to allow scraping from news sites, etc.
# DEFAULT_KEYWORDS and get_dynamic_keywords_from_llm are removed as the new
# LLM approach directly generates the analytical report from broad content.

# --- LLM Functions ---
def generate_company_update(company_name, text_content, tavily_search_results):
    """
    Uses Gemini LLM to analyze aggregated text and search results
    to generate a company update in three sections: What's new, Why it matters, and Watchlist.
    """
    print(f"\n-> Generating company update for {company_name} with Gemini LLM...")
    max_chars = 30000  # Increased max_chars for more extensive content to provide more context to the LLM
    if len(text_content) > max_chars:
        print(f"   (Trimming content from {len(text_content)} to {max_chars} characters for LLM analysis)")
        text_content = text_content[:max_chars]

    # Format Tavily search results for context to help the LLM identify sources and specific events
    context_string = ""
    if tavily_search_results:
        context_string = "\n\n--- Top Search Results for Context (Titles, Snippets, and URLs to help identify sources) ---\n"
        # Using top 5-7 results to provide concentrated source hints to the LLM
        for i, res in enumerate(tavily_search_results[:7]):
            source_domain = urlparse(res['url']).netloc.replace('www.', '')
            context_string += f"[{i+1}] Title: {res['title']}\n"
            context_string += f"    Source: {source_domain}\n"
            context_string += f"    Snippet: {res['content']}\n\n"
        context_string += "--------------------------------------------------------------------------\n\n"

    try:
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set.")
        genai.configure(api_key=gemini_api_key)
        model = genai.GenerativeModel('gemini-2.5-flash') # Using gemini-2.5-flash for speed

        # Dynamically determine the watchlist period for the current year
        current_year = datetime.datetime.now().year
        current_month = datetime.datetime.now().month
        if current_month <= 9: # If it's up to September, include Q3 and Q4
            watchlist_period = f"Q3–Q4 {current_year}"
        else: # If it's October-December, focus on Q4 and potentially Q1 of next year
            watchlist_period = f"Q4 {current_year} and Q1 {current_year + 1}"

        prompt = f"""
        You are a highly experienced financial analyst and market observer.
        Based on the following aggregated text from various reliable news articles, company announcements, investor pages, and other sources related to '{company_name}', generate a concise company update.

        Here is some additional context from top search results which may help you identify specific news outlets or official announcements for sourcing recent developments:
        {context_string}

        Aggregated Scraped Text for detailed analysis (contains more comprehensive content from the sources):
        ---
        {text_content}
        ---

        Structure your update *exactly* into the following three sections, using bullet points as shown in the example.
        For "What's new", aim to identify 3-5 distinct, significant developments for '{company_name}' from the *past approximately 2 to 6 weeks*.
        For "Why it matters", provide 2-3 key insights, explaining the significance of the news.
        For "Watchlist", list 2-4 key events, trends, or data points to monitor for the remainder of {current_year} (specifically {watchlist_period}).

        Format the output precisely as follows:

        1) {company_name}
        What’s new (last ~2–6 weeks)
        {'    '}• [Brief summary of a significant recent development]. [Source, e.g., Benzinga, MarketWatch, Company Investor Relations, Official Press Release]
        {'    '}• [Brief summary of another significant recent development]. [Source]
        {'    '}• ... (continue with 1-3 more bullet points)

        Why it matters (insights)
        1. [Insight 1]: Explain the significance, market impact, or strategic implications of the news. Connect the dots between recent developments and potential future trajectory. (If relevant, mention implications for suppliers or related industries.) [Cite specific news points or sources if an insight is directly tied to one piece of news; otherwise, this is your analytical synthesis]
        2. [Insight 2]: ... (provide 1-2 more key insights)

        Watchlist for {watchlist_period}:
        {'    '}• [Key event or trend to monitor, e.g., upcoming earnings call, new product launch, regulatory decision, market trend]. [Brief explanation or reason for monitoring]
        {'    '}• [Another key item to monitor].
        {'    '}• ... (continue with 1-2 more bullet points)

        IMPORTANT GUIDELINES:
        - Prioritize information that is recent, impactful, and directly relevant to {company_name}.
        - Do not invent facts, figures, or news. If specific details are not present, make a reasonable inference based on the overall context or avoid making a definitive claim.
        - For sources in "What's new", refer to the provided 'Top Search Results for Context' and the aggregated text to infer the most likely news outlet, company official source (e.g., 'Company Investor Relations', 'Press Release'), or use a more general attribution like 'Multiple News Outlets' if widely reported. Aim for specific names if possible.
        - Ensure all sections are distinct and clearly address the prompt's requirements for content and timeframe.
        - Maintain a professional, analytical tone suitable for a financial analyst.
        """
        response = model.generate_content([{"role": "user", "parts": [prompt]}], generation_config={"temperature": 0.3})
        return response.candidates[0].content.parts[0].text
    except Exception as e:
        return f"[Error] Could not generate company update from Gemini LLM: {e}"

# --- Scraping Functions ---
def simple_scrape(url):
    """Scrapes the text content of a single URL."""
    try:
        session = requests.Session()
        session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'})
        response = session.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        for script_or_style in soup(['script', 'style', 'header', 'footer', 'nav', 'aside']):
            script_or_style.extract()
        text = ' '.join(soup.get_text().split())
        print(f"   Scraped {len(text)} characters from {url}")
        return text
    except requests.RequestException as e:
        print(f"   [Error] Could not scrape {url}: {e}")
        return ""

# --- Main Workflow Function (Modified to return string) ---
def main_workflow(company_name):
    """
    Full workflow: search broadly for recent news and financial information,
    scrape multiple relevant sources, aggregate content, and generate a
    company update with "What's new", "Why it matters", and "Watchlist".
    Returns the generated report string or an error message.
    """
    print(f"Starting broad search for recent updates and financial information on '{company_name}'...")
    
    tavily_api_key = os.getenv('TAVILY_API_KEY')
    if not tavily_api_key:
        return "[Error] TAVILY_API_KEY environment variable not set. Please set it in your .env file."

    try:
        tavily_client = TavilyClient(api_key=tavily_api_key)
        search_query = f"{company_name} latest news OR recent financial update OR investor announcements OR market developments"
        response = tavily_client.search(query=search_query, search_depth="advanced", max_results=15)
        if not response or 'results' not in response:
            print("Tavily search failed or returned no results.")
            return "[Error] Tavily search failed or returned no results for company insights."
        search_results = response['results']
    except Exception as e:
        print(f"An error occurred during Tavily search: {e}")
        return f"[Error] An error occurred during Tavily search for company insights: {e}"

    potential_sources = [res['url'] for res in search_results if res.get('url')]

    if not potential_sources:
        print("No potential sources found after initial search.")
        return "[Error] No potential sources found for company insights."

    print(f"\n-> Scraping content from {len(potential_sources)} potential sources...")
    full_scraped_corpus = ""
    for i, url in enumerate(potential_sources):
        print(f"   Processing source {i+1}/{len(potential_sources)}: {url}")
        scraped_text = simple_scrape(url)
        if scraped_text:
            full_scraped_corpus += scraped_text + " "

    if not full_scraped_corpus:
        print("\n❌ Could not gather any text content from the search results.")
        return "\n❌ Could not gather any text content from the search results for company insights."

    if full_scraped_corpus and len(full_scraped_corpus) > 500:
        update_report = generate_company_update(company_name, full_scraped_corpus, search_results)
        return update_report
    else:
        print("\n❌ Could not gather enough text content from the websites to generate a detailed company update.")
        return "\n❌ Could not gather enough text content from the websites to generate a detailed company update."

# Remove or comment out the __main__ block if this file is primarily for import.
# If you keep it, ensure it's for independent testing only and doesn't interfere
# with Streamlit's execution.
if __name__ == "__main__":
    # Ensure you have your API keys set as environment variables.
    # For example:
    # os.environ['TAVILY_API_KEY'] = 'YOUR_TAVILY_API_KEY'
    # os.environ['GEMINI_API_KEY'] = 'YOUR_GEMINI_API_KEY'

    # Example:
    report = main_workflow("Tesla")
    print("\n" + "="*20 + " TESLA COMPANY UPDATE " + "="*20)
    print(report)
    print("="*70)