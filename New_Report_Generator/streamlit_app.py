# streamlit_app.py

import streamlit as st
import matplotlib.pyplot as plt
import pandas as pd
import json
import os
from dotenv import load_dotenv

# Import for PDF generation
import asyncio
from playwright.async_api import async_playwright
import time # Added for potential small delays if needed

# Import the backend analysis function from your other file
from economic_analyzer import run_analysis

# Import competitor analysis and plotting functions
from competitors_list_generator import get_competitors
from stock_plotting_tools import fetch_stock_data, plot_candles_stick, TickerRetrievalTool

# Import company insights function
from company_insights import main_workflow as get_company_insights

# Load environment variables at the very beginning of the app
load_dotenv()

# --- Initialize session state for report visibility ---
if 'show_economic_report' not in st.session_state:
    st.session_state.show_economic_report = False
if 'show_competitor_report' not in st.session_state:
    st.session_state.show_competitor_report = False
if 'economic_results' not in st.session_state:
    st.session_state.economic_results = None
if 'competitor_results' not in st.session_state:
    st.session_state.competitor_results = None


# --- Async PDF Generation Function ---
async def generate_pdf_from_url(url: str, output_path: str):
    """
    Generates a PDF of the given URL using Playwright.
    """
    async with async_playwright() as p:
        # Launch a headless Chromium browser
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        st.info(f"Navigating to {url} to capture content for PDF...")
        # Navigate to the Streamlit app URL.
        # 'wait_until="networkidle"' tries to wait until network activity has been low for a short while.
        # It's important to give enough time for all dynamic content (charts, LLM text) to load.
        await page.goto(url, wait_until="networkidle", timeout=90000) # Increased timeout
        
        # Ensure the content is fully rendered, especially plots and LLM responses.
        # A short delay can be very useful here. Adjust as needed.
        st.info("Waiting for page content to settle...")
        await page.wait_for_timeout(5000) # Wait 5 seconds for content to fully render
        
        st.info("Generating PDF...")
        # Generate PDF with A4 format and print background graphics
        # You can add more options here, e.g., scale, margin
        await page.pdf(path=output_path, format="A4", print_background=True, scale=0.8) # Adjusted scale for better fit
        await browser.close()
    return output_path


def create_plot(gdp_df: pd.DataFrame, inflation_df: pd.DataFrame, country_code: str):
    """Generates a Matplotlib figure of economic data."""
    
    # Ensure date column is datetime for proper plotting
    if gdp_df is not None and 'date' in gdp_df.columns:
        gdp_df['date'] = pd.to_datetime(gdp_df['date'])
    if inflation_df is not None and 'date' in inflation_df.columns:
        inflation_df['date'] = pd.to_datetime(inflation_df['date'])

    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax1 = plt.subplots(figsize=(12, 6))

    # Plot GDP Growth on the primary y-axis
    if gdp_df is not None and not gdp_df.empty:
        ax1.plot(gdp_df['date'], gdp_df['value'], 'b-', marker='o', label='GDP Growth (Annual %)')
        ax1.set_xlabel('Year')
        ax1.set_ylabel('GDP Growth (%)', color='b')
        ax1.tick_params(axis='y', labelcolor='b')

    # Create a second y-axis for Inflation
    ax2 = ax1.twinx()

    # Plot Inflation on the secondary y-axis
    if inflation_df is not None and not inflation_df.empty:
        ax2.plot(inflation_df['date'], inflation_df['value'], 'r-', marker='x', label='Inflation (Annual %)')
        ax2.set_ylabel('Inflation (%)', color='r')
        ax2.tick_params(axis='y', labelcolor='r')

    # Final plot touches
    plt.title(f'Economic Trends for {country_code.upper()}: GDP Growth vs. Inflation')
    fig.tight_layout()
    
    # Add a unified legend
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax2.legend(lines + lines2, labels + labels2, loc='upper left')
    
    return fig

# --- Streamlit Page Configuration ---
st.set_page_config(page_title="Market Analysis Report", layout="wide")

# Custom CSS from your provided code
st.markdown("""
    <style>
    .profile-header h1 {
        color: #556b3b; /* Adjusted color for our theme */
        font-size: 60px;
    }
    /* Clean Title */
    .title {
        color: var(--primary);
        text-align: center;
        font-weight: 600;
        margin: 2rem 0;
        font-size: 2.25rem;
        letter-spacing: -0.5px;
    }

    /* Smaller Containers */
    .stContainer {
        background: var(--surface);
        border-radius: 12px;
        padding: 1rem; /* Reduced from 1.5rem */
        margin-bottom: 1rem; /* Reduced from 1.5rem */
        border: 1px solid #30363D;
    }

    /* Message Cards (not directly used for analysis output, but kept for context) */
    .user-message {
        background: var(--surface);
        border-left: 4px solid var(--primary);
        border-radius: 8px;
        padding: 0.75rem; /* Reduced from 1rem */
        margin: 0.5rem 0; /* Reduced from 1rem 0 */
    }

    .assistant-message {
        background: var(--surface);
        border-left: 4px solid #30363D;
        border-radius: 8px;
        padding: 0.75rem; /* Reduced from 1rem */
        margin: 0.5rem 0; /* Reduced from 1rem 0 */
    }

    /* Smaller Input Form (applied to st.sidebar and specific elements) */
    /* Streamlit's sidebar is typically a separate div, so .stForm won't directly apply there.
       We'll rely on stTextInput for the text input in the sidebar. */

    .stTextInput>div>div>input {
        background: var(--background);
        color: var(--text-primary);
        border-radius: 8px;
        border: 1px solid #30363D;
        padding: 10px; /* Slightly reduced from 12px */
    }


    /* Minimal Plot Container */
    .plot-container {
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #30363D;
        padding: 1rem; /* Add some padding inside the plot container */
        background: var(--surface); /* Match other containers */
        margin-bottom: 1rem;
    }

    /* Custom container for insights */
    .insights-container {
        border-radius: 12px;
        border: 1px solid #30363D;
        padding: 1rem;
        background: var(--surface);
        margin-bottom: 1rem;
        min-height: 400px; /* Ensure a minimum height for the insights container */
        display: flex; /* Use flexbox to make content fill vertically */
        flex-direction: column;
    }
    .insights-container .stMarkdown {
        flex-grow: 1; /* Allow markdown content to grow and fill space */
    }


    /* Clean File Uploader (not directly used in this version) */
    .stFileUploader>label {
        border: 1px dashed #30363D;
        border-radius: 8px;
        background: var(--background);
        padding: 0.75rem; /* Reduced to match form */
    }
    /* Add this to your existing CSS */
    .attachment-button {
        display: none; /* Not used in this app */
    }
    
    .attachment-button:hover {
        background: #30363D;
        color: var(--text-primary);
    }
    
    .attachment-button svg {
        width: 20px;
        height: 20px;
    }
    
    .hidden-uploader {
        display: none;
    }
    
    /* Adjust the input columns */
    .input-columns {
        display: flex;
        align-items: center;
    }
    
    /* Section separator styling */
    .section-separator {
        margin: 2rem 0;
        border-bottom: 2px solid #30363D;
        padding-bottom: 1rem;
    }
    </style>
""", unsafe_allow_html=True)


# Updated title section to use the custom CSS classes
st.markdown("""
<div class="profile-header">
    <h1 style="text-align:center;">📈 Market Analysis Report</h1>
    <p style="text-align:center;">
        Your <span class="highlight">Economic Compass</span> — generating insights from global data!<br>
        Analyze economic trends or delve into competitor stock performance and AI-driven insights.
    </p>
</div>
""", unsafe_allow_html=True)

st.markdown("") # Add some space
st.markdown("")
st.markdown("")


# --- Sidebar for User Input ---
with st.sidebar:
    st.header("⚙️ Report Controls")
    
    st.subheader("Economic Analysis")
    country_code_input = st.text_input("Enter Country Code:", "SG", help="E.g., SG, US, CN, DE").upper()
    
    # Use a callback to update session state when button is clicked
    def on_economic_analyze_click():
        st.session_state.show_economic_report = True
        # Don't hide competitor report anymore
        if country_code_input:
            with st.spinner(f"Analyzing economic data for {country_code_input.upper()}..."):
                st.session_state.economic_results = run_analysis(country_code_input)
        else:
            st.warning("Please enter a country code for economic analysis.")
            st.session_state.show_economic_report = False

    st.button("Generate Economic Report", type="primary", on_click=on_economic_analyze_click)

    st.markdown("---") # Separator
    st.subheader("Competitor Stock & Insight Analysis")
    company_for_competitors = st.text_input("Company for Competitor Search:", "Google", help="e.g., Google, Apple")
    industry_for_competitors = st.text_input("Industry:", "Technology", help="e.g., Technology, Automotive")

    def on_competitor_analyze_click():
        st.session_state.show_competitor_report = True
        # Don't hide economic report anymore
        if company_for_competitors and industry_for_competitors:
            with st.spinner(f"Finding competitors for {company_for_competitors} and fetching data..."):
                try:
                    competitors = get_competitors(company_for_competitors, industry_for_competitors)
                    st.session_state.competitor_results = {
                        "company": company_for_competitors,
                        "industry": industry_for_competitors,
                        "competitors": competitors,
                        "country_hint": country_code_input # Pass country hint for ticker retrieval
                    }
                except ValueError as e:
                    st.error(f"Configuration error for competitor analysis: {e}. Ensure API keys are set.")
                    st.session_state.show_competitor_report = False
                except Exception as e:
                    st.error(f"An unexpected error occurred during competitor identification: {e}")
                    st.session_state.show_competitor_report = False
        else:
            st.warning("Please enter both a company name and an industry for competitor analysis.")
            st.session_state.show_competitor_report = False

    st.button("Analyze Competitors", type="secondary", on_click=on_competitor_analyze_click)

    # Add clear buttons section
    st.markdown("---")
    st.subheader("Clear Reports")
    
    def clear_economic_report():
        st.session_state.show_economic_report = False
        st.session_state.economic_results = None
    
    def clear_competitor_report():
        st.session_state.show_competitor_report = False
        st.session_state.competitor_results = None
    
    def clear_all_reports():
        st.session_state.show_economic_report = False
        st.session_state.show_competitor_report = False
        st.session_state.economic_results = None
        st.session_state.competitor_results = None
    
    col1, col2 = st.columns(2)
    with col1:
        st.button("Clear Economic", on_click=clear_economic_report, help="Clear economic analysis report")
    with col2:
        st.button("Clear Competitor", on_click=clear_competitor_report, help="Clear competitor analysis report")
    
    st.button("Clear All Reports", on_click=clear_all_reports, type="secondary", help="Clear both reports")

    # --- PDF Report Section ---
    st.markdown("---") 
    st.subheader("📥 Export to PDF")
    st.info("Ensure the desired reports are fully displayed on the main page before generating the PDF. This will capture *all visible content*.")

    # Get the Streamlit app URL.
    # For local development, it's typically http://localhost:8501
    # For a deployed app (e.g., Streamlit Cloud), you would need to know its public URL.
    # It's highly recommended to set this as an environment variable for deployment.
    # e.g., in your .env or Streamlit Cloud secrets: STREAMLIT_APP_URL="https://your-app-name.streamlit.app"
    streamlit_app_url = os.getenv("STREAMLIT_APP_URL", "http://localhost:8501")
    
    pdf_placeholder = st.empty() # Create a placeholder for messages/download button

    if st.button("Generate PDF Report"):
        if st.session_state.show_economic_report or st.session_state.show_competitor_report:
            with pdf_placeholder.container():
                with st.spinner("Generating PDF... This can take 15-30 seconds or more depending on content and server performance."):
                    pdf_filename = f"Market_Analysis_Report_{time.strftime('%Y%m%d_%H%M%S')}.pdf"
                    try:
                        # Run the async Playwright function
                        pdf_path = asyncio.run(generate_pdf_from_url(streamlit_app_url, pdf_filename))
                        
                        with open(pdf_path, "rb") as pdf_file:
                            st.download_button(
                                label="Download PDF Report",
                                data=pdf_file,
                                file_name=pdf_filename,
                                mime="application/pdf",
                                help="Click to download the generated PDF."
                            )
                        st.success("PDF generated successfully! Click 'Download PDF Report' above.")
                        os.remove(pdf_path) # Clean up the generated file after download
                    except Exception as e:
                        st.error(f"Failed to generate PDF: {e}")
                        st.warning("Please ensure Playwright browser binaries are installed (`playwright install`) and your Streamlit app is running at the specified URL.")
        else:
            pdf_placeholder.warning("No report is currently displayed to generate a PDF for. Please generate an economic or competitor report first.")


# --- Main Page Display ---

# Display initial instructions if no reports are active
if not st.session_state.show_economic_report and not st.session_state.show_competitor_report:
    st.info("Enter details in the sidebar and click a button to generate a report.")

# --- Economic Analysis Section (Always First) ---
if st.session_state.show_economic_report and st.session_state.economic_results:
    results = st.session_state.economic_results
    st.header(f"📊 Economic Analysis for {results.get('country_code', 'Selected Country')}")

    if results.get("error"):
        st.error(results["error"])
    else:
        st.subheader("Economic Trends: GDP vs. Inflation")
        st.markdown('<div class="plot-container">', unsafe_allow_html=True)
        fig = create_plot(results["gdp_df"], results["inflation_df"], results["country_code"])
        st.pyplot(fig)
        st.markdown('</div>', unsafe_allow_html=True)

        st.subheader("🤖 AI-Powered Economic Insights")
        st.markdown('<div class="stContainer">', unsafe_allow_html=True)
        st.markdown(results["llm_insights"])
        st.markdown('</div>', unsafe_allow_html=True)

        with st.expander("View Raw Economic Data", expanded=False):
            st.markdown('<div class="stContainer">', unsafe_allow_html=True)
            col1, col2 = st.columns(2)
            with col1:
                st.write("GDP Growth Data")
                st.dataframe(results["gdp_df"])
            with col2:
                st.write("Inflation Data")
                st.dataframe(results["inflation_df"])
            st.markdown('</div>', unsafe_allow_html=True)
    
    # Add section separator if both reports are showing
    if st.session_state.show_competitor_report and st.session_state.competitor_results:
        st.markdown('<div class="section-separator"></div>', unsafe_allow_html=True)

# --- Competitor Analysis Section (Always Second) ---
if st.session_state.show_competitor_report and st.session_state.competitor_results:
    competitor_data = st.session_state.competitor_results
    st.header(f"🏢 Competitor Analysis for {competitor_data['company']} in {competitor_data['industry']} industry")

    competitors = competitor_data["competitors"]
    country_hint = competitor_data["country_hint"]

    if not competitors:
        st.info(f"Could not find any direct competitors for {competitor_data['company']} in the {competitor_data['industry']} industry, or an error occurred.")
    else:
        st.subheader(f"Found Competitors:")
        st.markdown('<div class="stContainer">', unsafe_allow_html=True)
        st.write(", ".join(competitors))
        st.markdown('</div>', unsafe_allow_html=True)
        
        st.subheader("Detailed Competitor Reports: AI Insights & Stock Performance")
        ticker_retriever = TickerRetrievalTool()
        
        for competitor in competitors:
            st.markdown(f"### {competitor}")
            
            # Swapped column order and adjusted ratio: insights_col on left, plot_col on right
            insights_col, plot_col = st.columns([0.7, 0.3]) # Insights get 70%, plot gets 30%

            with insights_col:
                st.markdown('<div class="insights-container">', unsafe_allow_html=True)
                st.write(f"🤖 AI-Powered Insights")
                with st.spinner(f"Generating insights for {competitor}... This may take a moment."):
                    insights = get_company_insights(competitor)
                    if insights:
                        st.markdown(insights)
                    else:
                        st.info(f"Could not generate insights for {competitor}.")
                st.markdown('</div>', unsafe_allow_html=True)

            with plot_col:
                st.markdown('<div class="plot-container">', unsafe_allow_html=True)
                st.write(f"📈 Stock Candlestick Chart")
                
                competitor_ticker = None
                with st.spinner(f"Fetching ticker for {competitor}..."):
                    # Attempt to retrieve ticker using the country hint from economic analysis if available,
                    # otherwise try a broader search without a specific country.
                    competitor_ticker = ticker_retriever.get_ticker(competitor, country=country_hint)
                    if not competitor_ticker:
                        st.warning(f"Could not find a ticker for {competitor} in {country_hint}. Attempting a broader search.")
                        competitor_ticker = ticker_retriever.get_ticker(competitor) # Broad search
                
                if competitor_ticker:
                    with st.spinner(f"Fetching and plotting stock data for {competitor} ({competitor_ticker})..."):
                        try:
                            stock_df = fetch_stock_data(competitor_ticker, period="1y")
                            if not stock_df.empty:
                                fig = plot_candles_stick(stock_df, title=f"{competitor} ({competitor_ticker}) Candlestick Chart (1 Year)")
                                st.plotly_chart(fig, use_container_width=True)
                            else:
                                st.info(f"No stock data available for {competitor} ({competitor_ticker}).")
                        except Exception as e:
                            st.error(f"Error fetching/plotting stock data for {competitor} ({competitor_ticker}): {e}")
                else:
                    st.info(f"Could not find a stock ticker for {competitor}.")
                st.markdown('</div>', unsafe_allow_html=True)
            st.markdown("---") # Separator between competitor reports