# streamlit_app.py

import streamlit as st
import matplotlib.pyplot as plt
import pandas as pd
import os
from dotenv import load_dotenv

# Import the backend analysis functions from your other files
from economic_analyzer import run_analysis
from competitors_list_generator import get_competitors
from stock_plotting_tools import fetch_stock_data, plot_candles_stick, TickerRetrievalTool
from company_insights import main_workflow as get_company_insights

# Load environment variables
load_dotenv()

# --- Initialize Session State ---
if 'show_economic_report' not in st.session_state:
    st.session_state.show_economic_report = False
if 'show_competitor_report' not in st.session_state:
    st.session_state.show_competitor_report = False
if 'economic_results' not in st.session_state:
    st.session_state.economic_results = None
if 'competitor_results' not in st.session_state:
    st.session_state.competitor_results = None

# --- Plotting Functions ---
def create_economic_plot(gdp_df: pd.DataFrame, inflation_df: pd.DataFrame, country_code: str):
    """Generates a Matplotlib figure of economic data."""
    if gdp_df is not None and 'date' in gdp_df.columns:
        gdp_df['date'] = pd.to_datetime(gdp_df['date'])
    if inflation_df is not None and 'date' in inflation_df.columns:
        inflation_df['date'] = pd.to_datetime(inflation_df['date'])

    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax1 = plt.subplots(figsize=(12, 6))

    if gdp_df is not None and not gdp_df.empty:
        ax1.plot(gdp_df['date'], gdp_df['value'], 'b-', marker='o', label='GDP Growth (Annual %)')
        ax1.set_xlabel('Year')
        ax1.set_ylabel('GDP Growth (%)', color='b')
        ax1.tick_params(axis='y', labelcolor='b')

    ax2 = ax1.twinx()
    if inflation_df is not None and not inflation_df.empty:
        ax2.plot(inflation_df['date'], inflation_df['value'], 'r-', marker='x', label='Inflation (Annual %)')
        ax2.set_ylabel('Inflation (%)', color='r')
        ax2.tick_params(axis='y', labelcolor='r')

    plt.title(f'Economic Trends for {country_code.upper()}: GDP Growth vs. Inflation')
    fig.tight_layout()
    
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax2.legend(lines + lines2, labels + labels2, loc='upper left')
    
    return fig

# --- Streamlit Page Configuration ---
st.set_page_config(page_title="Market Analysis Report", layout="wide")

# Custom CSS for a cleaner look
st.markdown("""
    <style>
    .profile-header h1 {
        color: #556b3b;
        font-size: 60px;
    }
    .stTextInput>div>div>input {
        background: var(--background);
        color: var(--text-primary);
        border-radius: 8px;
        border: 1px solid #30363D;
        padding: 10px;
    }
    .section-separator {
        margin: 2rem 0;
        border-bottom: 2px solid #30363D;
        padding-bottom: 1rem;
    }
    </style>
""", unsafe_allow_html=True)

# --- Page Header ---
st.markdown("""
<div class="profile-header">
    <h1 style="text-align:center;">📈 Market Analysis Report</h1>
    <p style="text-align:center;">
        Your <span class="highlight">Economic Compass</span> — generating insights from global data!<br>
        Analyze economic trends or delve into competitor stock performance and AI-driven insights.
    </p>
</div>
""", unsafe_allow_html=True)
st.markdown("<br><br>", unsafe_allow_html=True)

# --- Sidebar Controls ---
with st.sidebar:
    st.header("⚙️ Report Controls")
    
    st.subheader("Economic Analysis")
    country_code_input = st.text_input("Enter Country Code:", "SG", help="E.g., SG, US, CN, DE").upper()
    
    def on_economic_analyze_click():
        st.session_state.show_economic_report = True
        if country_code_input:
            with st.spinner(f"Analyzing economic data for {country_code_input.upper()}..."):
                st.session_state.economic_results = run_analysis(country_code_input)
        else:
            st.warning("Please enter a country code.")
            st.session_state.show_economic_report = False

    st.button("Generate Economic Report", type="primary", on_click=on_economic_analyze_click)

    st.markdown("---")
    st.subheader("Competitor Analysis")
    company_for_competitors = st.text_input("Company for Competitor Search:", "Google", help="e.g., Google, Apple")
    industry_for_competitors = st.text_input("Industry:", "Technology", help="e.g., Technology, Automotive")

    def on_competitor_analyze_click():
        st.session_state.show_competitor_report = True
        if company_for_competitors and industry_for_competitors:
            with st.spinner(f"Finding competitors for {company_for_competitors}..."):
                try:
                    competitors = get_competitors(company_for_competitors, industry_for_competitors)[:3]
                    st.session_state.competitor_results = {
                        "company": company_for_competitors,
                        "industry": industry_for_competitors,
                        "competitors": competitors,
                        "country_hint": country_code_input
                    }
                except Exception as e:
                    st.error(f"An error occurred: {e}")
                    st.session_state.show_competitor_report = False
        else:
            st.warning("Please enter both a company and industry.")
            st.session_state.show_competitor_report = False

    st.button("Analyze Competitors", type="secondary", on_click=on_competitor_analyze_click)

    st.markdown("---")
    st.subheader("Clear Reports")
    
    def clear_all_reports():
        st.session_state.show_economic_report = False
        st.session_state.show_competitor_report = False
        st.session_state.economic_results = None
        st.session_state.competitor_results = None
    
    st.button("Clear All Reports", on_click=clear_all_reports, help="Clear all generated reports")

# --- UI IMPROVEMENT: Main Page Display with Tabs ---
tab1, tab2 = st.tabs(["📊 Economic Analysis", "🏢 Competitor Analysis"])

with tab1:
    if not st.session_state.show_economic_report:
        st.info("Use the sidebar to generate an economic report.")
    elif st.session_state.economic_results:
        results = st.session_state.economic_results
        
        if results.get("error"):
            st.error(results["error"])
        else:
            with st.container(border=True):
                st.subheader("Economic Trends: GDP vs. Inflation")
                fig = create_economic_plot(results["gdp_df"], results["inflation_df"], results["country_code"])
                st.pyplot(fig)

            with st.container(border=True):
                st.subheader("🤖 AI-Powered Economic Insights")
                st.markdown(results["llm_insights"])

            with st.expander("View Raw Economic Data"):
                col1, col2 = st.columns(2)
                with col1:
                    st.write("GDP Growth Data")
                    st.dataframe(results["gdp_df"])
                with col2:
                    st.write("Inflation Data")
                    st.dataframe(results["inflation_df"])

with tab2:
    if not st.session_state.show_competitor_report:
        st.info("Use the sidebar to generate a competitor analysis.")
    elif st.session_state.competitor_results:
        competitor_data = st.session_state.competitor_results
        st.header(f"Competitor Analysis for {competitor_data['company']} in {competitor_data['industry']}")

        competitors = competitor_data.get("competitors", [])
        country_hint = competitor_data.get("country_hint", "")

        if not competitors:
            st.info(f"Could not find direct competitors for {competitor_data['company']}.")
        else:
            st.subheader(f"Found Competitors:")
            with st.container(border=True):
                st.markdown("- " + "\n- ".join(competitors))
            
            st.subheader("Detailed Competitor Reports")
            
            for competitor in competitors:
                st.markdown(f"### {competitor}")
                insights_col, plot_col = st.columns([0.65, 0.35])

                with insights_col:
                    with st.container(border=True):
                        st.markdown("🤖 **AI-Powered Insights**")
                        with st.spinner(f"Generating insights for {competitor}..."):
                            insights = get_company_insights(competitor)
                            st.markdown(insights if insights else f"Could not generate insights for {competitor}.")

                with plot_col:
                    with st.container(border=True):
                        st.markdown("📈 **Stock Candlestick Chart (1Y)**")
                        
                        ticker = None
                        with st.spinner(f"Fetching ticker for {competitor}..."):
                            ticker_retriever = TickerRetrievalTool()
                            ticker = ticker_retriever.get_ticker(competitor, country=country_hint) or ticker_retriever.get_ticker(competitor)
                        
                        if ticker:
                            with st.spinner(f"Plotting stock data for {ticker}..."):
                                try:
                                    stock_df = fetch_stock_data(ticker, period="1y")
                                    if not stock_df.empty:
                                        fig_stock = plot_candles_stick(stock_df, title=f"{competitor} ({ticker})")
                                        st.plotly_chart(fig_stock, use_container_width=True)
                                    else:
                                        st.info(f"No stock data found for {ticker}.")
                                except Exception as e:
                                    st.error(f"Error plotting data for {ticker}: {e}")
                        else:
                            st.info(f"Could not find a stock ticker for {competitor}.")
                st.markdown("---")