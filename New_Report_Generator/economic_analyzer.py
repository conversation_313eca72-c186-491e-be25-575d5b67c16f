# economic_analyzer.py

import os
import requests
import pandas as pd
from datetime import datetime
from typing import Dict, TypedDict, Optional
from dotenv import load_dotenv

# Langchain and Groq imports
from langchain_groq import ChatGroq
from langgraph.graph import StateGraph, END

# --- FreeEconomicDataAPI Class (Your original code, no changes needed) ---
class FreeEconomicDataAPI:
    """
    Comprehensive API client for free economic data sources.
    """
    # ... (paste your entire FreeEconomicDataAPI class here, unchanged)
    def __init__(self, fred_api_key: Optional[str] = None):
        self.fred_api_key = fred_api_key
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Economic-Data-Client/1.0'})
    def get_world_bank_data(self, country_code: str, indicator: str, start_year: str = "2010", end_year: str = "2024") -> Dict:
        url = f"https://api.worldbank.org/v2/countries/{country_code}/indicators/{indicator}"
        params = {'format': 'json', 'date': f"{start_year}:{end_year}", 'per_page': 1000}
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            return {'source': 'World Bank', 'country': country_code, 'indicator': indicator, 'data': data[1] if len(data) > 1 else []}
        except Exception as e:
            print(f"Error fetching World Bank data for {country_code} ({indicator}): {e}")
            return {'source': 'World Bank', 'error': str(e)}
    def to_dataframe(self, data: Dict) -> pd.DataFrame:
        try:
            source = data.get('source', '').lower()
            if source == 'world bank':
                df = pd.DataFrame(data.get('data', []))
                if not df.empty and 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    if 'value' in df.columns:
                        df['value'] = pd.to_numeric(df['value'], errors='coerce')
                    df = df.sort_values('date', ascending=False)
                return df
            return pd.DataFrame(data)
        except Exception as e:
            print(f"Error converting to DataFrame: {e}")
            return pd.DataFrame()


# --- Langgraph Setup for Economic Insights ---

class EconomicAgentState(TypedDict):
    """Represents the state of our economic analysis workflow."""
    api_client: FreeEconomicDataAPI
    country_code: str
    gdp_growth_df: Optional[pd.DataFrame]
    inflation_df: Optional[pd.DataFrame]
    gdp_growth_str: str
    inflation_str: str
    analysis_prompt: str
    llm_insights: str

def fetch_economic_data(state: EconomicAgentState) -> Dict:
    api_client = state["api_client"]
    country_code = state["country_code"]
    gdp_data_raw = api_client.get_world_bank_data(country_code, 'NY.GDP.MKTP.KD.ZG', start_year="2010", end_year="2024")
    inflation_data_raw = api_client.get_world_bank_data(country_code, 'FP.CPI.TOTL.ZG', start_year="2010", end_year="2024")
    gdp_df = api_client.to_dataframe(gdp_data_raw).dropna(subset=['value'])
    inflation_df = api_client.to_dataframe(inflation_data_raw).dropna(subset=['value'])
    return {"gdp_growth_df": gdp_df, "inflation_df": inflation_df}

def format_data_for_llm(state: EconomicAgentState) -> Dict:
    gdp_str = state["gdp_growth_df"][['date', 'value']].to_markdown(index=False) if not state["gdp_growth_df"].empty else "No GDP Growth data available."
    inflation_str = state["inflation_df"][['date', 'value']].to_markdown(index=False) if not state["inflation_df"].empty else "No Inflation data available."
    return {"gdp_growth_str": gdp_str, "inflation_str": inflation_str}

def prepare_analysis_prompt(state: EconomicAgentState) -> dict:
    prompt = (
        f"You are an expert economic analyst. Your task is to analyze the provided "
        f"economic data for the country with code '{state['country_code']}' and offer concise, actionable insights, "
        "identifying key trends, year-over-year changes, and potential implications. "
        f"Focus on GDP growth and inflation figures.\n\n"
        f"--- {state['country_code']} GDP Growth (Annual %) ---\n"
        f"{state['gdp_growth_str']}\n\n"
        f"--- {state['country_code']} Inflation (Annual %) ---\n"
        f"{state['inflation_str']}\n\n"
        "Please provide your economic insights, highlighting: \n"
        "1. Key trends in GDP growth and inflation over the period.\n"
        "2. Notable positive or negative changes and specific years.\n"
        "3. Any apparent correlations between GDP and inflation.\n"
        "4. A brief overall assessment of the country's recent economic performance and potential future outlook.\n"
        "Your response should be structured with clear headings and bullet points."
    )
    return {"analysis_prompt": prompt}

def get_llm_economic_insights(state: EconomicAgentState) -> dict:
    llm = ChatGroq(model="meta-llama/llama-4-maverick-17b-128e-instruct", temperature=0.0)
    try:
        response = llm.invoke(state["analysis_prompt"])
        return {"llm_insights": response.content}
    except Exception as e:
        error_message = f"Error: Could not retrieve insights due to API call failure: {e}"
        return {"llm_insights": error_message}

# --- Main Analysis Function ---
def run_analysis(country_code: str):
    """
    Runs the full economic analysis workflow for a given country code.
    """
    load_dotenv()
    if "GROQ_API_KEY" not in os.environ:
        return {"error": "GROQ_API_KEY environment variable not set."}

    workflow = StateGraph(EconomicAgentState)
    workflow.add_node("fetch_data", fetch_economic_data)
    workflow.add_node("format_data", format_data_for_llm)
    workflow.add_node("prepare_prompt", prepare_analysis_prompt)
    workflow.add_node("get_insights", get_llm_economic_insights)
    workflow.set_entry_point("fetch_data")
    workflow.add_edge("fetch_data", "format_data")
    workflow.add_edge("format_data", "prepare_prompt")
    workflow.add_edge("prepare_prompt", "get_insights")
    workflow.add_edge("get_insights", END)
    app = workflow.compile()

    economic_api_client = FreeEconomicDataAPI(fred_api_key=os.getenv("FRED_API_KEY"))
    initial_state = {
        "api_client": economic_api_client,
        "country_code": country_code.upper(),
    }

    final_state = app.invoke(initial_state)

    # Return the key results for the Streamlit app
    return {
        "llm_insights": final_state.get("llm_insights", "No insights generated."),
        "gdp_df": final_state.get("gdp_growth_df"),
        "inflation_df": final_state.get("inflation_df"),
        "country_code": country_code.upper()
    }