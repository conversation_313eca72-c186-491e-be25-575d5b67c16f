# stock_plotting_tools.py

import yfinance as yf
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import requests
import logging
from typing import Optional, Dict, Any

# We are not using the LangChain specific parts (get_company_country, parse_query, generate_plot, format_response)
# from the reference tools directly in the Streamlit app's competitor plotting flow.
# Instead, we will directly use TickerRetrievalTool and the plotting functions.

def fetch_stock_data(ticker: str, period: str = "1y") -> pd.DataFrame:
    """Fetches historical stock data using yfinance."""
    stock = yf.Ticker(ticker)
    return stock.history(period=period)

def fetch_balance(ticker: str, tp: str = "Annual") -> pd.DataFrame:
    """Fetches balance sheet data using yfinance."""
    ticker_obj = yf.Ticker(ticker)
    bs = ticker_obj.balance_sheet if tp == "Annual" else ticker_obj.quarterly_balance_sheet
    return bs.loc[:, bs.isna().mean() < 0.5]

def plot_candles_stick(df: pd.DataFrame, title: str = "") -> go.Figure:
    """Generates a Plotly Candlestick chart."""
    if df.empty or 'Open' not in df.columns or 'High' not in df.columns or \
       'Low' not in df.columns or 'Close' not in df.columns:
        fig = go.Figure()
        fig.update_layout(title=f"{title} - No candlestick data available")
        return fig

    fig = go.Figure(data=[go.Candlestick(x=df.index,
                open=df['Open'],
                high=df['High'],
                low=df['Low'],
                close=df['Close'])])
    fig.update_layout(title=title, xaxis_rangeslider_visible=False)
    return fig

def plot_balance(df: pd.DataFrame, ticker: str = "", currency: str = "") -> go.Figure:
    """Generates a Plotly Bar chart for balance sheet."""
    if df.empty:
        fig = go.Figure()
        fig.update_layout(title=f"Accounting Balance: {ticker} - No data available")
        return fig
    
    # Ensure columns are datetime objects before formatting
    df.columns = pd.to_datetime(df.columns).strftime('%b %d, %Y')
    
    components = {
        'Total Assets': {'color': 'forestgreen', 'name': 'Assets'},
        'Stockholders Equity': {'color': 'CornflowerBlue', 'name': "Stockholder's Equity"},
        'Total Liabilities Net Minority Interest': {'color': 'tomato', 'name': "Total Liabilities"},
    }
    
    fig = go.Figure()
    
    # Check if necessary rows exist before plotting
    if 'Total Assets' in df.index:
        fig.add_trace(go.Bar(
            x=[df.columns, ['Assets'] * len(df.columns)],
            y=df.loc['Total Assets'],
            name=components['Total Assets']['name'],
            marker=dict(color=components['Total Assets']['color'])
        ))
    
    if 'Stockholders Equity' in df.index and 'Total Liabilities Net Minority Interest' in df.index:
        fig.add_trace(go.Bar(
            x=[df.columns, ['L+E'] * len(df.columns)],
            y=df.loc['Stockholders Equity'],
            name=components['Stockholders Equity']['name'],
            marker=dict(color=components['Stockholders Equity']['color'])
        ))
        fig.add_trace(go.Bar(
            x=[df.columns, ['L+E'] * len(df.columns)],
            y=df.loc['Total Liabilities Net Minority Interest'],
            name=components['Total Liabilities Net Minority Interest']['name'],
            marker=dict(color=components['Total Liabilities Net Minority Interest']['color'])
        ))

    # Annotations (add checks for data presence)
    offset = 0.03 * df.loc['Total Assets'].max() if 'Total Assets' in df.index and not df.empty and not df.loc['Total Assets'].empty else 0
    for i, date in enumerate(df.columns):
        if 'Total Assets' in df.index and not pd.isna(df.loc['Total Assets', date]):
            fig.add_annotation(
                x=[date, "Assets"],
                y=df.loc['Total Assets', date] / 2,
                text=str(round(df.loc['Total Assets', date] / 1e9, 1)) + 'B',
                showarrow=False,
                font=dict(size=12, color="black"),
                align="center"
            )
            if i > 0 and 'Total Assets' in df.index and not pd.isna(df.loc['Total Assets', date]) and not pd.isna(df.loc['Total Assets'].iloc[i - 1]):
                percentage = round((df.loc['Total Assets'].iloc[i] / df.loc['Total Assets'].iloc[i - 1] - 1) * 100, 1)
                sign = '+' if percentage >= 0 else ''
                fig.add_annotation(
                    x=[date, "Assets"],
                    y=df.loc['Total Assets', date] + offset,
                    text=sign + str(percentage) + '%',
                    showarrow=False,
                    font=dict(size=12, color="black"),
                    align="center"
                )

        if 'Total Liabilities Net Minority Interest' in df.index and 'Stockholders Equity' in df.index and \
           not pd.isna(df.loc['Total Liabilities Net Minority Interest', date]) and not pd.isna(df.loc['Stockholders Equity', date]) and \
           'Total Assets' in df.index and not pd.isna(df.loc['Total Assets', date]):
            percentage = round((df.loc['Total Liabilities Net Minority Interest', date] / df.loc['Total Assets', date]) * 100, 1)
            fig.add_annotation(
                x=[date, "L+E"],
                y=df.loc['Stockholders Equity', date] + df.loc['Total Liabilities Net Minority Interest', date] / 2,
                text=str(percentage) + '%',
                showarrow=False,
                font=dict(size=12, color="black"),
                align="center"
            )

    fig.update_layout(
        barmode='stack',
        title=f'Accounting Balance: {ticker}',
        xaxis_title='Year',
        yaxis_title=f'Amount (in {currency})',
        legend_title='Balance components',
    )
    return fig

def plot_assets(df: pd.DataFrame, ticker: str = "", currency: str = "") -> go.Figure:
    """Generates a Plotly Subplot for Current and Non-Current Assets."""
    if df.empty:
        fig = make_subplots(rows=1, cols=2, shared_yaxes=True, horizontal_spacing=0.05,
                            subplot_titles=['Current Assets', 'Non-Current Assets'])
        fig.update_layout(title=f"Assets: {ticker} - No data available")
        return fig

    assets_components = {
        'Current Assets': [
            'Cash Cash Equivalents And Short Term Investments',
            'Receivables',
            'Prepaid Assets',
            'Inventory',
            'Hedging Assets Current',
            'Other Current Assets'
        ],
        'Total Non Current Assets': [
            'Net PPE',
            'Goodwill And Other Intangible Assets',
            'Investments And Advances',
            'Investment Properties',
            'Other Non Current Assets'
        ]
    }

    fig = make_subplots(
        rows=1, cols=2,
        shared_yaxes=True,
        horizontal_spacing=0.05,
        subplot_titles=['Current Assets', 'Non-Current Assets']
    )

    # Ensure columns are datetime objects before formatting
    df.columns = pd.to_datetime(df.columns).strftime('%b %d, %Y')

    colors = px.colors.sequential.Blugrn[::-1]
    i = 0
    for component in assets_components['Current Assets']:
        if component in df.index and not df.loc[component].empty:
            fig.add_trace(go.Bar(
                x=df.columns,
                y=df.loc[component],
                name=component,
                marker=dict(color=colors[i % len(colors)]),
                legendgroup='Current Assets',
                showlegend=True
            ), row=1, col=1)
            i += 1

    colors = px.colors.sequential.Purp[::-1]
    i = 0
    for component in assets_components['Total Non Current Assets']:
        if component in df.index and not df.loc[component].empty:
            fig.add_trace(go.Bar(
                x=df.columns,
                y=df.loc[component],
                name=component,
                marker=dict(color=colors[i % len(colors)]),
                legendgroup='Non-current Assets',
                showlegend=True
            ), row=1, col=2)
            i += 1

    # Annotations
    offset = 0.03 * df.loc['Total Assets'].max() if 'Total Assets' in df.index and not df.empty and not df.loc['Total Assets'].empty else 0
    for i, date in enumerate(df.columns):
        if 'Current Assets' in df.index and not pd.isna(df.loc['Current Assets', date]):
            fig.add_annotation(
                x=date,
                y=df.loc['Current Assets', date] + offset,
                text=str(round(df.loc['Current Assets', date] / 1e9, 1)) + 'B',
                showarrow=False,
                font=dict(size=12, color="black"),
                align="center",
                row=1, col=1
            )
        if 'Total Non Current Assets' in df.index and not pd.isna(df.loc['Total Non Current Assets', date]):
            fig.add_annotation(
                x=date,
                y=df.loc['Total Non Current Assets', date] + offset,
                text=str(round(df.loc['Total Non Current Assets', date] / 1e9, 1)) + 'B',
                showarrow=False,
                font=dict(size=12, color="black"),
                align="center",
                row=1, col=2
            )

    fig.update_layout(
        barmode='stack',
        title=f'Assets: {ticker}',
        xaxis1=dict(title='Date', type='category'),
        xaxis2=dict(title='Date', type='category'),
        yaxis_title=f'Amount (in {currency})',
        legend_title='Asset Components',
        hovermode="x unified"
    )
    return fig

class TickerRetrievalTool:
    """
    Retrieves stock ticker symbols for companies across different countries.
    Assumes `requests` is installed.
    """
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)
        self.yfinance_url = "https://query2.finance.yahoo.com/v1/finance/search"
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'

    def get_ticker(self, company_name: str, country: str = None) -> Optional[str]:
        """
        Retrieve stock ticker for a given company and optionally a country.
        If country is None, it tries a broader search.
        """
        try:
            params = {
                "q": company_name,
                "quotes_count": 5,
            }
            if country:
                params["country"] = country

            response = requests.get(
                url=self.yfinance_url,
                params=params,
                headers={'User-Agent': self.user_agent},
                timeout=10
            )
            response.raise_for_status()
            data = response.json()

            quotes = data.get('quotes', [])
            if not quotes:
                self.logger.warning(f"No quotes found for {company_name} in {country if country else 'any country'}")
                return None

            # Prioritize exact matches or primary exchanges if country is specified
            if country:
                country_upper = country.upper()
                for quote in quotes:
                    # Specific handling for India's NSI exchange
                    if country_upper == 'INDIA' and quote.get('exchange') == 'NSI':
                        return quote.get('symbol')
                    # General check for name match (shortname or longname)
                    if quote.get('shortname', '').upper() == company_name.upper() or \
                       quote.get('longname', '').upper() == company_name.upper():
                        return quote.get('symbol')
                    # Common exchanges for US
                    if quote.get('exchange', '').upper() in ['NASDAQ', 'NYSE'] and country_upper == 'US':
                         return quote.get('symbol')
            
            # Fallback: just take the first symbol from the list if nothing specific matched
            if quotes:
                return quotes[0].get('symbol')
            return None

        except requests.RequestException as e:
            self.logger.error(f"Network error retrieving ticker for {company_name}: {e}")
            return None
        except (KeyError, IndexError) as e:
            self.logger.warning(f"No ticker found for {company_name} in {country if country else 'any country'} due to response parsing: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error in ticker retrieval for {company_name}: {e}")
            return None