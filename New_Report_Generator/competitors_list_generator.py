# competitors_list_generator.py

import os
from typing import List

# To load environment variables from a .env file (if this file is run standalone, otherwise main app handles it)
from dotenv import load_dotenv

# LangChain components for interacting with the language model
from langchain_groq import ChatGroq
from langchain_core.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser

# Tavily for performing the web search
from tavily import TavilyClient

def get_competitors(company: str, industry: str) -> List[str]:
    """
    Finds and returns a list of competitors for a given company and industry.

    This function performs two main steps:
    1.  Uses the Tavily search API to find articles and text about the
        company's competitive landscape.
    2.  Feeds this text to a language model (LLM) with a specific prompt
        to extract only the names of the competitors.

    Args:
        company: The name of the company to research.
        industry: The industry or domain of the company.

    Returns:
        A list of strings, where each string is the name of a competitor.
        Returns an empty list if no competitors are found.
    """
    # Load environment variables if not already loaded (e.g., if run directly)
    if os.getenv('TAVILY_API_KEY') is None or os.getenv('GROQ_API_KEY') is None:
        load_dotenv()

    print(f"\nResearching competitors for {company} in the {industry} industry...")

    # --- Step 1: Perform a targeted web search ---
    print("Searching the web for market data...")
    search_query = f"key direct competitors of {company} in the {industry} market"
    
    # Initialize the Tavily client and perform the search
    api_key = os.environ.get('TAVILY_API_KEY')
    if not api_key:
        raise ValueError("TAVILY_API_KEY environment variable not set. Please set it in your .env file.")
    tavily_client = TavilyClient(api_key=api_key)
    
    try:
        # We only need 2-3 good sources to find the top competitors
        search_results = tavily_client.search(query=search_query, max_results=3)['results']
        # Combine the content of the search results into a single string
        search_context = "\n\n".join([result['content'] for result in search_results])
    except Exception as e:
        print(f"Error during web search: {e}")
        return []

    # --- Step 2: Use an LLM to extract competitor names from the search results ---
    print("Extracting competitor names from search results...")

    extraction_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are an expert market analyst. Your sole task is to extract company names from a given text."),
        ("human", """Based on the following text, identify the main competitors of {company}.

        <text>
        {search_context}
        </text>

        Instructions:
        1. Return ONLY a comma-separated list of the competitor names.
        2. Do NOT include the original company, '{company}', in the list.
        3. Do NOT add any introduction, explanation, or conclusion.
        4. Example format: Competitor A, Competitor B, Competitor C""")
    ])

    llm_api_key = os.getenv('GROQ_API_KEY')
    if not llm_api_key:
        raise ValueError("GROQ_API_KEY environment variable not set. Please set it in your .env file.")
    llm = ChatGroq(model_name='Gemma2-9b-it', api_key=llm_api_key)

    extraction_chain = extraction_prompt | llm | StrOutputParser()
    competitor_string = extraction_chain.invoke({
        "company": company,
        "search_context": search_context
    })

    # --- Step 3: Clean up and return the list ---
    if competitor_string:
        competitors = [name.strip() for name in competitor_string.split(',') if name.strip()]
        return competitors
    else:
        return []

if __name__ == "__main__":
    load_dotenv() # Load API keys if running this file directly

    print("--- Competitor Finder ---")
    
    try:
        company_name = input("Enter the company name: ").strip()
        industry_name = input("Enter the industry or domain: ").strip()

        competitor_list = get_competitors(company_name, industry_name)

        if competitor_list:
            print(f"\nFound the following competitors for {company_name}:")
            for competitor in competitor_list:
                print(f"- {competitor}")
        else:
            print(f"\nCould not find any direct competitors for {company_name} in the specified domain.")

    except ValueError as e:
        print(f"\nError: {e}")
        print("Please make sure you have a .env file with TAVILY_API_KEY and GROQ_API_KEY.")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")