import boto3
import sys

# --- Configuration ---
# Replace these placeholder values with your actual details

# The AWS region where your Bedrock resources are located
REGION_NAME = "us-east-1" 
# The unique ID of your Bedrock Knowledge Base
KNOWLEDGE_BASE_ID = "NZW3OFZAJP"
# The ARN of the foundation model to use for generating responses
MODEL_ARN = "arn:aws:bedrock:us-east-1::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0"

# --- Initialization ---
try:
    # Initialize the Boto3 client for the Bedrock Agent Runtime
    bedrock_agent_runtime = boto3.client(
        "bedrock-agent-runtime",
        region_name=REGION_NAME
    )
except Exception as e:
    print(f"Error initializing Boto3 client: {e}")
    sys.exit(1)


def query_knowledge_base(query):
    """
    Sends a query to the Bedrock Knowledge Base and prints the response.
    """
    try:
        # Call the RetrieveAndGenerate API
        response = bedrock_agent_runtime.retrieve_and_generate(
            input={'text': query},
            retrieveAndGenerateConfiguration={
                'type': 'KNOWLEDGE_BASE',
                'knowledgeBaseConfiguration': {
                    'knowledgeBaseId': KNOWLEDGE_BASE_ID,
                    'modelArn': MODEL_ARN
                }
            }
        )
        
        # Extract and print the response and sources
        answer = response['output']['text']
        citations = response.get('citations', [])

        print("\n✅ Answer:")
        print(answer)

        if citations:
            print("\n📚 Sources:")
            # Iterate through citations and their retrieved references
            for citation in citations:
                for reference in citation.get('retrievedReferences', []):
                    s3_uri = reference['location']['s3Location']['uri']
                    print(f"  - {s3_uri}")

    except Exception as e:
        print(f"❌ An error occurred while querying the knowledge base: {e}")


# --- Main Execution Loop ---
if __name__ == "__main__":
    print("🤖 Chat with your Knowledge Base (type 'exit' to quit)")
    while True:
        user_query = input("\nAsk a question: ")
        if user_query.lower() == 'exit':
            break
        if user_query:
            query_knowledge_base(user_query)